<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I
use Codeception\Module;

class Api extends Module
{
    public function getFirstIdFromResponse(string $response): string
    {
        $responseJson = json_decode(json: $response, associative: true);

        if (!is_array(value: $responseJson) || !isset($responseJson['items']) || !is_array(value: $responseJson['items'])) {
            throw new \Exception(message: 'Invalid response format');
        }

        $data = $responseJson['items'];
        if ([] === $data || !is_array(value: $data[0]) || !isset($data[0]['id'])) {
            throw new \Exception(message: 'No types found or invalid type format');
        }

        $dataId = $data[0]['id'];
        if (!is_string(value: $dataId) && !is_int(value: $dataId)) {
            throw new \Exception(message: 'Type ID must be string or integer');
        }

        return (string) $dataId;
    }
}
