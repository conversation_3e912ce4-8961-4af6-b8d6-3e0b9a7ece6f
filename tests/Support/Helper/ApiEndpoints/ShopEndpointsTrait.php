<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait ShopEndpointsTrait
{
    /**
     * @throws \Exception
     */
    public function getTypeOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/shop/v1/type-options'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function getSectorOptionsByType(
        string $type,
        int $expectedStatusCode = 200,
        bool $debug = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/shop/v1/types/'.$type.'/sector-options'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function getLicenseYearOptionsByType(
        string $type,
        int $expectedStatusCode = 200,
        bool $debug = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/shop/v1/types/'.$type.'/license-year-options'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function getQuantityOptionsByLicenseYearId(
        string $licenseYearId,
        int $expectedStatusCode = 200,
        bool $debug = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/shop/v1/license-years/'.$licenseYearId.'/quantity-options'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function getQuantityOptionContentsByQuantityOption(
        string $quantityOption,
        int $expectedStatusCode = 200,
        bool $debug = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/shop/v1/quantity-options/'.$quantityOption.'/options'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
