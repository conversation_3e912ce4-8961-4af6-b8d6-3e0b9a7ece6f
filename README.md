## Setup development environment
1. Clone the repo
    ```sh
    <NAME_EMAIL>:prezero/license-shop-sf.git
    ```

1. Copy ``auth.json.dist`` and rename it to ``auth.json``
    ```sh
    cp auth.json.dist auth.json
    ```
1. Add your github token to ``auth.json``
    ```json
    {
      "github-oauth": {
        "github.com": "ghp_abcdefghijklmnopqrstuvwxyz"
      }
    }
    ```

1. Start docker containers
    ```sh
    docker-compose up -d
    ```

1. Go inside container
    ```sh
    docker exec -it example_backend bash
    ```
   or to execute docker commands from local machine
    ```sh
    docker-compose exec backend <command>
    ```

1. Install composer packages
    ```sh
    composer install
    ```

1. Activate githooks
    ```sh
   php vendor/bin/captainhook install
    ```

1. Migrate database
    ```sh
    php bin/console doctrine:migration:migrate
    ```

1. Generate fixtures
    ```sh
    php bin/console hautelook:fixtures:load
    ```

1. Run all tests
    ```sh
    php vendor/bin/codecept run
    ```

Application is accessible on host machine port 8000 (http://localhost:8000)
