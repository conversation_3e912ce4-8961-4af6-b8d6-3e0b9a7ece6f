<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818204133 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE quantity_selection (id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, model_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_96A71327975B7E7 ON quantity_selection (model_id)');
        $this->addSql('ALTER TABLE quantity_selection ADD CONSTRAINT FK_96A71327975B7E7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_selection DROP CONSTRAINT FK_96A71327975B7E7');
        $this->addSql('DROP TABLE quantity_selection');
    }
}
