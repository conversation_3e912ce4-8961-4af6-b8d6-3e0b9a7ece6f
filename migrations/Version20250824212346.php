<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250824212346 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE quantity_inquiry_term_condition (selected BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, quantity_inquiry_id UUID NOT NULL, term_condition_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_C49469D37752B4FB ON quantity_inquiry_term_condition (quantity_inquiry_id)');
        $this->addSql('CREATE INDEX IDX_C49469D3D5EE6B42 ON quantity_inquiry_term_condition (term_condition_id)');
        $this->addSql('ALTER TABLE quantity_inquiry_term_condition ADD CONSTRAINT FK_C49469D37752B4FB FOREIGN KEY (quantity_inquiry_id) REFERENCES quantity_inquiry (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE quantity_inquiry_term_condition ADD CONSTRAINT FK_C49469D3D5EE6B42 FOREIGN KEY (term_condition_id) REFERENCES term_condition (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE term_condition ADD active BOOLEAN DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry_term_condition DROP CONSTRAINT FK_C49469D37752B4FB');
        $this->addSql('ALTER TABLE quantity_inquiry_term_condition DROP CONSTRAINT FK_C49469D3D5EE6B42');
        $this->addSql('DROP TABLE quantity_inquiry_term_condition');
        $this->addSql('ALTER TABLE term_condition DROP active');
    }
}
