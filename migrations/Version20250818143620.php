<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818143620 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE cost_overview (text VARCHAR(200) NOT NULL, sequence INT NOT NULL, area VARCHAR(20) DEFAULT NULL, currency VARCHAR(5) DEFAULT NULL, price DOUBLE PRECISION DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, model_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_51E3920E7975B7E7 ON cost_overview (model_id)');
        $this->addSql('ALTER TABLE cost_overview ADD CONSTRAINT FK_51E3920E7975B7E7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cost_overview DROP CONSTRAINT FK_51E3920E7975B7E7');
        $this->addSql('DROP TABLE cost_overview');
    }
}
