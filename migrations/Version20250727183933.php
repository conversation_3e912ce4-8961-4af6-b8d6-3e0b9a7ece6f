<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250727183933 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE included_service (text VARCHAR(100) NOT NULL, sequence INT NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, model_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_596BF4C97975B7E7 ON included_service (model_id)');
        $this->addSql('CREATE TABLE model (type VARCHAR(25) NOT NULL, title VARCHAR(25) NOT NULL, subtitle VARCHAR(50) NOT NULL, price VARCHAR(25) NOT NULL, price_subtitle VARCHAR(50) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE TABLE quantity_option (parent_id UUID DEFAULT NULL, name VARCHAR(25) NOT NULL, input_type VARCHAR(25) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE TABLE quantity_option_content (parent_id UUID DEFAULT NULL, name VARCHAR(25) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, quantity_option_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_F37A509A380B608A ON quantity_option_content (quantity_option_id)');
        $this->addSql('ALTER TABLE included_service ADD CONSTRAINT FK_596BF4C97975B7E7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE quantity_option_content ADD CONSTRAINT FK_F37A509A380B608A FOREIGN KEY (quantity_option_id) REFERENCES quantity_option (id) NOT DEFERRABLE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE included_service DROP CONSTRAINT FK_596BF4C97975B7E7');
        $this->addSql('ALTER TABLE quantity_option_content DROP CONSTRAINT FK_F37A509A380B608A');
        $this->addSql('DROP TABLE included_service');
        $this->addSql('DROP TABLE model');
        $this->addSql('DROP TABLE quantity_option');
        $this->addSql('DROP TABLE quantity_option_content');
    }
}
