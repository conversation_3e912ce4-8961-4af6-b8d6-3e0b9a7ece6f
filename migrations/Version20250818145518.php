<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818145518 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cost_overview ALTER text TYPE VARCHAR(250)');
        $this->addSql('ALTER TABLE cost_overview_text ALTER text TYPE VARCHAR(250)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cost_overview ALTER text TYPE VARCHAR(200)');
        $this->addSql('ALTER TABLE cost_overview_text ALTER text TYPE VARCHAR(200)');
    }
}
