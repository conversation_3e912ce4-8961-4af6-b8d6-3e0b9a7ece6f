<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250824191048 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE quantity_selection_content (row INT NOT NULL, value VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, quantity_selection_id UUID NOT NULL, quantity_option_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_A3051006DAED3436 ON quantity_selection_content (quantity_selection_id)');
        $this->addSql('CREATE INDEX IDX_A3051006380B608A ON quantity_selection_content (quantity_option_id)');
        $this->addSql('ALTER TABLE quantity_selection_content ADD CONSTRAINT FK_A3051006DAED3436 FOREIGN KEY (quantity_selection_id) REFERENCES quantity_selection (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE quantity_selection_content ADD CONSTRAINT FK_A3051006380B608A FOREIGN KEY (quantity_option_id) REFERENCES quantity_option (id) NOT DEFERRABLE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_selection_content DROP CONSTRAINT FK_A3051006DAED3436');
        $this->addSql('ALTER TABLE quantity_selection_content DROP CONSTRAINT FK_A3051006380B608A');
        $this->addSql('DROP TABLE quantity_selection_content');
    }
}
