<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250824193858 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry ADD contact_title VARCHAR(5) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD contact_firstname VARCHAR(50) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD contact_lastname VARCHAR(50) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD contact_phone_number VARCHAR(50) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD contact_email VARCHAR(254) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD company_name VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD address VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD address_info VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD postal_code VARCHAR(25) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD city VARCHAR(50) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD country VARCHAR(3) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD vat_id VARCHAR(20) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD registration_number VARCHAR(20) NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD product_type_registration_number VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD industry_code VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD einvoice_email VARCHAR(254) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry DROP contact_title');
        $this->addSql('ALTER TABLE quantity_inquiry DROP contact_firstname');
        $this->addSql('ALTER TABLE quantity_inquiry DROP contact_lastname');
        $this->addSql('ALTER TABLE quantity_inquiry DROP contact_phone_number');
        $this->addSql('ALTER TABLE quantity_inquiry DROP contact_email');
        $this->addSql('ALTER TABLE quantity_inquiry DROP company_name');
        $this->addSql('ALTER TABLE quantity_inquiry DROP address');
        $this->addSql('ALTER TABLE quantity_inquiry DROP address_info');
        $this->addSql('ALTER TABLE quantity_inquiry DROP postal_code');
        $this->addSql('ALTER TABLE quantity_inquiry DROP city');
        $this->addSql('ALTER TABLE quantity_inquiry DROP country');
        $this->addSql('ALTER TABLE quantity_inquiry DROP vat_id');
        $this->addSql('ALTER TABLE quantity_inquiry DROP registration_number');
        $this->addSql('ALTER TABLE quantity_inquiry DROP product_type_registration_number');
        $this->addSql('ALTER TABLE quantity_inquiry DROP industry_code');
        $this->addSql('ALTER TABLE quantity_inquiry DROP einvoice_email');
    }
}
