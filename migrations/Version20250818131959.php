<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818131959 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE model ADD currency VARCHAR(5) DEFAULT NULL');
        $this->addSql('ALTER TABLE model ADD price_text VARCHAR(25) DEFAULT NULL');
        $this->addSql('ALTER TABLE model DROP price');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE model ADD price VARCHAR(25) NOT NULL');
        $this->addSql('ALTER TABLE model DROP currency');
        $this->addSql('ALTER TABLE model DROP price_text');
    }
}
