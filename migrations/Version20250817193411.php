<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250817193411 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_option_content ADD billing_unit VARCHAR(3) DEFAULT NULL');
        $this->addSql('ALTER TABLE quantity_option_content ADD external_id VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE quantity_option_content ADD price DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_option_content DROP billing_unit');
        $this->addSql('ALTER TABLE quantity_option_content DROP external_id');
        $this->addSql('ALTER TABLE quantity_option_content DROP price');
    }
}
