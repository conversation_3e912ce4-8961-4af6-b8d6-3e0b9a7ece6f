<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818204428 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry DROP CONSTRAINT fk_a98dcaf57975b7e7');
        $this->addSql('DROP INDEX uniq_a98dcaf57975b7e7');
        $this->addSql('ALTER TABLE quantity_inquiry DROP model_id');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry ADD model_id UUID NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD CONSTRAINT fk_a98dcaf57975b7e7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX uniq_a98dcaf57975b7e7 ON quantity_inquiry (model_id)');
    }
}
