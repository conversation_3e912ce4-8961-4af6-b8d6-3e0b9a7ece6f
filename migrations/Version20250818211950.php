<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818211950 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX uniq_96a71327975b7e7');
        $this->addSql('CREATE INDEX IDX_96A71327975B7E7 ON quantity_selection (model_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_96A71327975B7E7');
        $this->addSql('CREATE UNIQUE INDEX uniq_96a71327975b7e7 ON quantity_selection (model_id)');
    }
}
