<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818204626 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry ADD quantity_selection_id UUID NOT NULL');
        $this->addSql('ALTER TABLE quantity_inquiry ADD CONSTRAINT FK_A98DCAF5DAED3436 FOREIGN KEY (quantity_selection_id) REFERENCES quantity_selection (id) NOT DEFERRABLE');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A98DCAF5DAED3436 ON quantity_inquiry (quantity_selection_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE quantity_inquiry DROP CONSTRAINT FK_A98DCAF5DAED3436');
        $this->addSql('DROP INDEX UNIQ_A98DCAF5DAED3436');
        $this->addSql('ALTER TABLE quantity_inquiry DROP quantity_selection_id');
    }
}
