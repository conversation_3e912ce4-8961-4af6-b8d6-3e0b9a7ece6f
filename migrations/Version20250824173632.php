<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250824173632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE agreement (name VARCHAR(255) NOT NULL, required BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, model_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_2E655A247975B7E7 ON agreement (model_id)');
        $this->addSql('CREATE TABLE quantity_inquiry_agreement (selected BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, quantity_inquiry_id UUID NOT NULL, agreement_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_A27833847752B4FB ON quantity_inquiry_agreement (quantity_inquiry_id)');
        $this->addSql('CREATE INDEX IDX_A278338424890B2B ON quantity_inquiry_agreement (agreement_id)');
        $this->addSql('CREATE TABLE term_condition (name VARCHAR(100) NOT NULL, description VARCHAR(100) NOT NULL, url VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, model_id UUID NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_7155A9A87975B7E7 ON term_condition (model_id)');
        $this->addSql('ALTER TABLE agreement ADD CONSTRAINT FK_2E655A247975B7E7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE quantity_inquiry_agreement ADD CONSTRAINT FK_A27833847752B4FB FOREIGN KEY (quantity_inquiry_id) REFERENCES quantity_inquiry (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE quantity_inquiry_agreement ADD CONSTRAINT FK_A278338424890B2B FOREIGN KEY (agreement_id) REFERENCES agreement (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE term_condition ADD CONSTRAINT FK_7155A9A87975B7E7 FOREIGN KEY (model_id) REFERENCES model (id) NOT DEFERRABLE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE agreement DROP CONSTRAINT FK_2E655A247975B7E7');
        $this->addSql('ALTER TABLE quantity_inquiry_agreement DROP CONSTRAINT FK_A27833847752B4FB');
        $this->addSql('ALTER TABLE quantity_inquiry_agreement DROP CONSTRAINT FK_A278338424890B2B');
        $this->addSql('ALTER TABLE term_condition DROP CONSTRAINT FK_7155A9A87975B7E7');
        $this->addSql('DROP TABLE agreement');
        $this->addSql('DROP TABLE quantity_inquiry_agreement');
        $this->addSql('DROP TABLE term_condition');
    }
}
