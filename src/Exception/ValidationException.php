<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\ConstraintViolationListInterface;

class ValidationException extends \RuntimeException implements \JsonSerializable
{
    public static function createException(
        int $returnCode,
        string $message,
        string $fieldName,
        mixed $value,
    ): ValidationException {
        $violationList = new ConstraintViolationList();
        $violationList->add(violation: new ConstraintViolation(message: $message, messageTemplate: null, parameters: [], root: null, propertyPath: $fieldName, invalidValue: $value));

        return new ValidationException(violationList: $violationList, code: $returnCode);
    }

    public function __construct(
        public readonly ConstraintViolationListInterface $violationList,
        int $code = Response::HTTP_BAD_REQUEST,
    ) {
        parent::__construct('invalid data', $code);
    }

    /**
     * @return array<ConstraintViolationInterface>
     */
    public function getConstraintViolation(): array
    {
        return iterator_to_array(iterator: $this->violationList);
    }

    /**
     * @return array<array<string, mixed>>
     */
    public function getResponseData(): array
    {
        $ret = [];

        foreach ($this->violationList as $violation) {
            /** @var ConstraintViolation $violation */
            $ret[] = [
                $violation->getPropertyPath() => $violation->getInvalidValue(),
                'message' => $violation->getMessage(),
            ];
        }

        return $ret;
    }

    /**
     * @return array<string, mixed>
     */
    public function jsonSerialize(): array
    {
        return [
            'class' => static::class,
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'violations' => $this->getResponseData(),
        ];
    }
}
