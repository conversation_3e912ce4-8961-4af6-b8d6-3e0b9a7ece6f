<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\CountryEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new GetCollection(
            controller: [CountryEndpoint::class, 'getOptions'],
            uriTemplate: '/quantity-selections/{quantitySelectionId}/country-options',
            pathParameters: [new PathParameter(name: 'quantitySelectionId', constraint: Requirement::UUID)],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['options']],
            responseOpenApiSchemaName: 'CountryOptionListReponse',
        ),
    ],
    identifier: 'id',
    tag: 'QuantityInquiry',
)]
class Country
{
    #[SerializedName('id')]
    #[Groups(['options'])]
    public string $id;

    #[SerializedName('display')]
    #[Groups(['options'])]
    public string $display;
}
