<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\FileEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new Get(
            controller: [FileEndpoint::class, 'get'],
            pathParameters: [new PathParameter(name: 'id')],
            requestDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
            responseOpenApiSchemaName: 'FileItemResponse',
        ),
    ],
    identifier: 'id',
    tag: 'File',
)]
class File
{
    #[SerializedName('id')]
    public string $id;

    public \DateTimeImmutable $date;

    public string $name;

    public string $origin;
}
