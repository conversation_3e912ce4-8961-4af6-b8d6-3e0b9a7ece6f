<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\QuantityInquiryEndpoint;
use App\Infrastructure\Shop\Resource\Dto\Checkbox;
use App\Infrastructure\Shop\Resource\Dto\Company;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new Get(
            controller: [QuantityInquiryEndpoint::class, 'getInformation'],
            uriTemplate: '/quantity-selections/{quantitySelectionId}/quantity-inquiries/information',
            pathParameters: [new PathParameter(name: 'quantitySelectionId', constraint: Requirement::UUID)],
            normalizationContext: ['groups' => ['information']],
            responseOpenApiSchemaName: 'QuantityInquiryTextResponse'
        ),
        new GetCollection(
            controller: [QuantityInquiryEndpoint::class, 'getCheckboxOptions'],
            uriTemplate: '/quantity-selections/{quantitySelectionId}/quantity-inquiries/checkbox-options',
            pathParameters: [new PathParameter(name: 'quantitySelectionId', constraint: Requirement::UUID)],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['checkbox_options']],
            responseOpenApiSchemaName: 'QuantityInquiryCheckboxOptionResponse'
        ),
        new Post(
            controller: [QuantityInquiryEndpoint::class, 'createQuantityInquiry'],
            uriTemplate: '/quantity-selections/{quantitySelectionId}/quantity-inquiries',
            pathParameters: [new PathParameter(name: 'quantitySelectionId', constraint: Requirement::UUID)],
            denormalizationContext: ['groups' => ['request']],
            requestOpenApiSchemaName: 'QuantityInquiryRequest',
            normalizationContext: ['groups' => ['response']],
            successHttpCode: 200,
            responseOpenApiSchemaName: 'QuantityInquiryResponse',
        ),
    ],
    identifier: 'id',
    tag: 'QuantityInquiry',
)]
class QuantityInquiry
{
    #[SerializedName('id')]
    #[Groups(['checkbox_options', 'country_options', 'response'])]
    public string $id;

    #[SerializedName('display')]
    #[Groups(['checkbox_options', 'country_options'])]
    public string $display;

    #[SerializedName('required')]
    #[Groups(['checkbox_options'])]
    public bool $required;

    #[SerializedName('company')]
    #[Groups(['request', 'response'])]
    public Company $company;

    /** @var Checkbox[] */
    #[SerializedName('checkboxes')]
    #[Groups(['request', 'response'])]
    public array $checkboxes;

    #[SerializedName('quantity_selection_id')]
    #[Groups(['request', 'response', 'information'])]
    public string $quantitySelectionId;

    #[SerializedName('title')]
    #[Groups(['information'])]
    public string $title;

    #[SerializedName('information')]
    #[Groups(['information'])]
    public string $information;
}
