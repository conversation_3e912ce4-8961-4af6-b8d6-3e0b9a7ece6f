<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\QuantitySelectionEndpoint;
use App\Infrastructure\Shop\Resource\Dto\IncludedService;
use App\Infrastructure\Shop\Resource\Dto\Price;
use App\Infrastructure\Shop\Resource\Dto\Quantity;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new Post(
            controller: [QuantitySelectionEndpoint::class, 'calculateQuantitySelection'],
            uriTemplate: '/product-types/{productType}/quantity-selections',
            pathParameters: [new PathParameter(name: 'productType')],
            denormalizationContext: ['groups' => ['request']],
            requestOpenApiSchemaName: 'QuantitySelectionRequest',
            normalizationContext: ['groups' => ['response']],
            successHttpCode: 200,
            responseOpenApiSchemaName: 'QuantitySelectionResponse',
        ),
    ],
    identifier: null,
    tag: 'QuantitySelection',
)]
class QuantitySelection
{
    #[SerializedName('id')]
    #[Groups(['response'])]
    public string $id;

    /** @var Quantity[] */
    #[SerializedName('quantities')]
    #[Groups(['request', 'response'])]
    public array $quantities;

    /** @var IncludedService[] */
    #[SerializedName('included_services')]
    #[Groups(['response'])]
    public array $includedServices = [];

    /** @var IncludedService[] */
    #[SerializedName('not_included_services')]
    #[Groups(['response'])]
    public array $notIncludedServices = [];

    /** @var IncludedService[] */
    #[SerializedName('information')]
    #[Groups(['response'])]
    public array $information = [];

    #[SerializedName('price_title')]
    #[Groups(['response'])]
    public string $priceTitle = '';

    /** @var Price[] */
    #[SerializedName('price_list')]
    #[Groups(['response'])]
    public array $priceList = [];

    /** @var Price[] */
    #[SerializedName('price_sum')]
    #[Groups(['response'])]
    public array $priceSum = [];

    #[SerializedName('price_subtitle')]
    #[Groups(['response'])]
    public string $priceSubtitle = '';

    #[SerializedName('model_type')]
    #[Groups(['response'])]
    public string $modelType;
}
