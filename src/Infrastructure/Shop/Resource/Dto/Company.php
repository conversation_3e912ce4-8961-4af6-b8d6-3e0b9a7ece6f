<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Company
{
    #[SerializedName('name')]
    #[Groups(['request', 'response'])]
    public string $name;

    #[SerializedName('address')]
    #[Groups(['request', 'response'])]
    public string $address;

    #[SerializedName('address_info')]
    #[Groups(['request', 'response'])]
    public string $addressInfo;

    #[SerializedName('postal_code')]
    #[Groups(['request', 'response'])]
    public string $postalCode;

    #[SerializedName('city')]
    #[Groups(['request', 'response'])]
    public string $city;

    #[SerializedName('country_id')]
    #[Groups(['request', 'response'])]
    public string $countryId;

    #[SerializedName('vat_id')]
    #[Groups(['request', 'response'])]
    public string $vatId;

    #[SerializedName('registration_number')]
    #[Groups(['request', 'response'])]
    public string $registrationNumber;

    #[SerializedName('product_type_registration_number')]
    #[Groups(['request', 'response'])]
    public string $productTypeRegistrationNumber;

    #[SerializedName('einvoice_email')]
    #[Groups(['request', 'response'])]
    public ?string $eInvoiceEmail = null;

    #[SerializedName('industry_code')]
    #[Groups(['request', 'response'])]
    public ?string $industryCode = null;

    #[SerializedName('contact_person')]
    #[Groups(['request', 'response'])]
    public ContactPerson $contactPerson;
}
