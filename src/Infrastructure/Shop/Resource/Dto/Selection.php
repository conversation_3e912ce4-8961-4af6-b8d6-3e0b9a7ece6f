<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

class Selection
{
    #[SerializedName('quantity_option_id')]
    #[Groups(['request', 'response'])]
    #[Assert\Uuid]
    public string $quantityOptionId;

    #[SerializedName('quantity_option_value')]
    #[Groups(['request', 'response'])]
    public string|int|float $quantityOptionValue;
}
