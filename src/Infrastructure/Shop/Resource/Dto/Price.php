<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Price
{
    #[SerializedName('text')]
    #[Groups(['response'])]
    public string $text;

    #[SerializedName('text_highlight')]
    #[Groups(['response'])]
    public bool $textHighlight;

    #[SerializedName('price')]
    #[Groups(['response'])]
    public string $price;

    #[SerializedName('price_highlight')]
    #[Groups(['response'])]
    public bool $priceHighlight;
}
