<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Checkbox
{
    #[SerializedName('checkbox_id')]
    #[Groups(['request', 'response'])]
    public string $checkboxId;

    #[SerializedName('selected')]
    #[Groups(['request', 'response'])]
    public bool $selected;
}
