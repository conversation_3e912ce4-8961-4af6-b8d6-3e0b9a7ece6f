<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Model
{
    #[SerializedName('id')]
    #[Groups(['response'])]
    public string $id;

    #[SerializedName('type')]
    #[Groups(['response'])]
    public string $type;

    #[SerializedName('title')]
    #[Groups(['response'])]
    public string $title;

    #[SerializedName('subtitle')]
    #[Groups(['response'])]
    public string $subtitle;

    #[SerializedName('price')]
    #[Groups(['response'])]
    public string $price;

    #[SerializedName('price_subtitle')]
    #[Groups(['response'])]
    public ?string $price_subtitle = null;

    /** @var IncludedService[] */
    #[SerializedName('included_services')]
    #[Groups(['response'])]
    public array $includedServices;
}
