<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource\Dto;

use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class ContactPerson
{
    #[SerializedName('title')]
    #[Groups(['request', 'response'])]
    public ?string $title = null;

    #[SerializedName('first_name')]
    #[Groups(['request', 'response'])]
    public string $firstName;

    #[SerializedName('last_name')]
    #[Groups(['request', 'response'])]
    public string $lastName;

    #[SerializedName('phone_number')]
    #[Groups(['request', 'response'])]
    public string $phoneNumber;

    #[SerializedName('email')]
    #[Groups(['request', 'response'])]
    public string $email;
}
