<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\PricingModelEndpoint;
use App\Infrastructure\Shop\Resource\Dto\IncludedService;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new GetCollection(
            controller: [PricingModelEndpoint::class, 'getPricingModels'],
            uriTemplate: '/product-types/{productType}/pricing-model-overviews',
            pathParameters: [new PathParameter(name: 'productType')],
            denormalizationContext: ['groups' => ['collection']],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['collection']],
            responseOpenApiSchemaName: 'PricingModelOverviewListReponse',
        ),
    ],
    identifier: null,
    tag: 'Start',
)]
class PricingModel
{
    #[SerializedName('title')]
    #[Groups(['collection'])]
    public string $title;

    #[SerializedName('subtitle')]
    #[Groups(['collection'])]
    public string $subtitle;

    #[SerializedName('price')]
    #[Groups(['collection'])]
    public string $price;

    #[SerializedName('price_subtitle')]
    #[Groups(['collection'])]
    public ?string $price_subtitle = null;

    /** @var IncludedService[] */
    #[SerializedName('included_services')]
    #[Groups(['collection'])]
    public array $includedServices;
}
