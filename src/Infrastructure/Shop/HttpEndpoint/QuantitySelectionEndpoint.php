<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Entity\Enum\IncludedServicePage;
use App\Domain\Entity\Enum\ModelType;
use App\Domain\Entity\Enum\ProductType;
use App\Domain\Entity\Enum\QuantityInputType;
use App\Domain\Entity\Model as ModelEntity;
use App\Domain\Entity\QuantityOption;
use App\Domain\Entity\QuantityOptionContent;
use App\Domain\Entity\QuantitySelection as QuantitySelectionEntity;
use App\Domain\Entity\QuantitySelectionContent;
use App\Domain\Repository\CostOverviewRepository;
use App\Domain\Repository\CostOverviewTextRepository;
use App\Domain\Repository\IncludedServiceRepository;
use App\Domain\Repository\ModelRepository;
use App\Domain\Repository\QuantityOptionContentRepository;
use App\Domain\Repository\QuantityOptionRepository;
use App\Domain\Service\BattGService;
use App\Domain\Service\WEEEService;
use App\Exception\NotFoundException;
use App\Infrastructure\Shop\Resource\Dto\IncludedService;
use App\Infrastructure\Shop\Resource\Dto\Price;
use App\Infrastructure\Shop\Resource\QuantitySelection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class QuantitySelectionEndpoint
{
    public function __construct(
        private BattGService $battGService,
        private WEEEService $weeeService,
        private ModelRepository $modelRepository,
        private IncludedServiceRepository $includedServiceRepository,
        private CostOverviewRepository $costOverviewRepository,
        private CostOverviewTextRepository $costOverviewTextRepository,
        private EntityManagerInterface $entityManager,
        private QuantityOptionRepository $quantityOptionRepository,
        private QuantityOptionContentRepository $quantityOptionContentRepository,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function calculateQuantitySelection(QuantitySelection $quantitySelectionDto, string $productType): QuantitySelection
    {
        $selectedProductType = ProductType::tryFrom(value: $productType);
        if (null === $selectedProductType) {
            throw new NotFoundException(message: "Product type $productType not found");
        }

        $amount = 0;
        $category = null;
        if (ProductType::BATTG == $selectedProductType) {
            $amount = $this->battGService->calculate(quantitySelection: $quantitySelectionDto);
        } elseif (ProductType::WEEE == $selectedProductType) {
            $amount = $this->weeeService->calculate(quantitySelection: $quantitySelectionDto);
            $category = $this->weeeService->getCategories(quantitySelection: $quantitySelectionDto);
        }

        $models = $this->modelRepository->findBy(criteria: ['productType' => $selectedProductType->value], orderBy: ['sequence' => 'ASC']);
        if ([] == $models) {
            throw new NotFoundException(message: 'Model not found');
        }

        usort(array: $models, callback: fn ($a, $b): int => $a->getThresholdMin() <=> $b->getThresholdMin());

        $selectedModel = null;
        foreach ($models as $model) {
            if ($model->getThresholdMin() <= $amount && $model->getThresholdMax() >= $amount) {
                $selectedModel = $model;
            }
        }

        if (null === $selectedModel) {
            throw new NotFoundException(message: 'Model not found');
        }

        $quantitySelection = $this->createQuantitySelectionEntity(quantitySelectionDto: $quantitySelectionDto, selectedModel: $selectedModel, amount: $amount);
        $quantitySelectionDto->id = $quantitySelection->getId();

        $includedServices = $this->includedServiceRepository->findBy(criteria: ['model' => $selectedModel, 'page' => IncludedServicePage::COST->value], orderBy: ['sequence' => 'ASC']);
        foreach ($includedServices as $includedService) {
            $includedServiceDto = new IncludedService();
            $includedServiceDto->text = $includedService->getText();

            if ($includedService->getIncluded()) {
                $quantitySelectionDto->includedServices[] = $includedServiceDto;
            } else {
                $quantitySelectionDto->notIncludedServices[] = $includedServiceDto;
            }
        }

        $information = $this->costOverviewTextRepository->findBy(criteria: ['model' => $selectedModel], orderBy: ['sequence' => 'ASC']);
        foreach ($information as $info) {
            $textDto = new IncludedService();
            $textDto->text = $info->getText();
            $quantitySelectionDto->information[] = $textDto;
        }

        $sum = 0;
        $currency = '';

        $criteria = ['model' => $selectedModel, 'area' => 'TOP'];
        if (null !== $category) {
            $criteria['category'] = $category;
        }

        $costOverviews = $this->costOverviewRepository->findBy(criteria: $criteria, orderBy: ['sequence' => 'ASC']);
        foreach ($costOverviews as $costOverview) {
            $priceDto = new Price();
            $priceDto->text = $costOverview->getText();
            $priceDto->price = $costOverview->getPriceText() ?? '';

            $priceDto->textHighlight = $costOverview->getTextHighlight() ?? false;
            $priceDto->priceHighlight = $costOverview->getPriceTextHighlight() ?? false;

            $currency = empty($currency) ? ($costOverview->getCurrency() ?? '') : $currency;

            $priceReplace = $costOverview->getPrice();
            if (null !== $priceReplace) {
                $sum += $costOverview->getPrice();
                $priceDto->text = str_replace(search: '{{price}}', replace: number_format(num: $priceReplace, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->text);
                $priceDto->price = str_replace(search: '{{price}}', replace: number_format(num: $priceReplace, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->price);
            } elseif (str_contains(haystack: $priceDto->text, needle: '{{amount}}') || str_contains(haystack: $priceDto->price, needle: '{{amount}}')) {
                $sum += $amount;
            }

            $priceDto->text = str_replace(search: '{{amount}}', replace: number_format(num: $amount, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->text);
            $priceDto->price = str_replace(search: '{{amount}}', replace: number_format(num: $amount, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->price);

            if (null !== $costOverview->getCurrency()) {
                $priceDto->text = str_replace(search: '{{currency}}', replace: $costOverview->getCurrency(), subject: $priceDto->text);
                $priceDto->price = str_replace(search: '{{currency}}', replace: $costOverview->getCurrency(), subject: $priceDto->price);
            }

            $quantitySelectionDto->priceList[] = $priceDto;
        }

        $costOverviews = $this->costOverviewRepository->findBy(criteria: ['model' => $selectedModel, 'area' => 'BOTTOM'], orderBy: ['sequence' => 'ASC']);
        foreach ($costOverviews as $costOverview) {
            $priceDto = new Price();
            $priceDto->text = $costOverview->getText();
            $priceDto->price = $costOverview->getPriceText() ?? '';

            $priceDto->textHighlight = $costOverview->getTextHighlight() ?? false;
            $priceDto->priceHighlight = $costOverview->getPriceTextHighlight() ?? false;

            if (null !== $costOverview->getPrice()) {
                $priceReplace = $costOverview->getPrice();
                $priceDto->text = str_replace(search: '{{price}}', replace: number_format(num: $priceReplace, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->text);
                $priceDto->price = str_replace(search: '{{price}}', replace: number_format(num: $priceReplace, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->price);
            }

            $priceDto->text = str_replace(search: '{{sum}}', replace: number_format(num: $sum, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->text);
            $priceDto->price = str_replace(search: '{{sum}}', replace: number_format(num: $sum, decimals: 2, decimal_separator: ',', thousands_separator: '.'), subject: $priceDto->price);

            $priceDto->text = str_replace(search: '{{currency}}', replace: $currency, subject: $priceDto->text);
            $priceDto->price = str_replace(search: '{{currency}}', replace: $currency, subject: $priceDto->price);

            $quantitySelectionDto->priceSum[] = $priceDto;
        }

        $modelType = ModelType::tryFrom(value: $selectedModel->getType());
        $quantitySelectionDto->modelType = $modelType->value ?? '';
        $quantitySelectionDto->priceTitle = 'Ihre jährlichen Kosten Modell '.$selectedModel->getTitle();

        return $quantitySelectionDto;
    }

    private function createQuantitySelectionEntity(QuantitySelection $quantitySelectionDto, ModelEntity $selectedModel, float $amount): QuantitySelectionEntity
    {
        $quantitySelection = new QuantitySelectionEntity();
        $quantitySelection->setModel(model: $selectedModel);
        $quantitySelection->setAmount(amount: $amount);
        $this->entityManager->persist($quantitySelection);

        $row = 0;
        foreach ($quantitySelectionDto->quantities as $quantity) {
            ++$row;
            foreach ($quantity->selections as $selection) {
                $quantityOption = $this->quantityOptionRepository->findOneBy(criteria: ['productType' => $selectedModel->getProductType(), 'name' => $selection->quantityOptionId]);

                if (!$quantityOption instanceof QuantityOption) {
                    throw new \Exception(message: 'Quantity option not found: '.$selection->quantityOptionId);
                }

                $value = '';
                if ($quantityOption->getInputType() === QuantityInputType::DROPDOWN->value) {
                    if (false === uuid_is_valid(uuid: $selection->quantityOptionValue)) {
                        throw new \Exception(message: 'Invalid quantity option id: '.$selection->quantityOptionValue);
                    }

                    $quantityOptionContent = $this->quantityOptionContentRepository->find(id: $selection->quantityOptionValue);
                    if (!$quantityOptionContent instanceof QuantityOptionContent) {
                        throw new \Exception(message: 'Quantity option content not found: '.$selection->quantityOptionValue);
                    }

                    $value = $quantityOptionContent->getId();
                } elseif ($quantityOption->getInputType() === QuantityInputType::NUMBER->value) {
                    $value = (string) $selection->quantityOptionValue;
                }

                $quantitySelectionContent = new QuantitySelectionContent();
                $quantitySelectionContent->setQuantitySelection(quantitySelection: $quantitySelection);
                $quantitySelectionContent->setRow(row: $row);
                $quantitySelectionContent->setQuantityOption(quantityOption: $quantityOption);
                $quantitySelectionContent->setValue(value: $value);
                $this->entityManager->persist($quantitySelectionContent);
            }
        }

        $this->entityManager->flush();

        return $quantitySelection;
    }
}
