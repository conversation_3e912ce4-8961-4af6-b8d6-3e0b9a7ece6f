<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Entity\Enum\Classification;
use App\Domain\Entity\Enum\ModelType;
use App\Domain\Entity\Enum\ProductType;
use App\Domain\Entity\QuantityInquiry as QuantityInquiryEntity;
use App\Domain\Entity\QuantityInquiryAgreement;
use App\Domain\Entity\QuantityInquiryTermCondition;
use App\Domain\Entity\QuantitySelection;
use App\Domain\Entity\QuantitySelectionContent;
use App\Domain\Entity\TermCondition;
use App\Domain\Repository\AgreementRepository;
use App\Domain\Repository\CostOverviewRepository;
use App\Domain\Repository\QuantityOptionContentRepository;
use App\Domain\Repository\QuantitySelectionRepository;
use App\Domain\Repository\TermConditionRepository;
use App\Domain\Service\Mailer;
use App\Exception\NotFoundException;
use App\Infrastructure\Shop\Resource\QuantityInquiry;
use Doctrine\ORM\EntityManagerInterface;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

#[AsController]
readonly class QuantityInquiryEndpoint
{
    public function __construct(
        private Mailer $mailer,
        #[Autowire('%env(FILE_ENDPOINT)%')]
        private string $fileEndpoint,
        private EntityManagerInterface $entityManager,
        private QuantitySelectionRepository $quantitySelectionRepository,
        private QuantityOptionContentRepository $quantityOptionContentRepository,
        private AgreementRepository $agreementRepository,
        private CostOverviewRepository $costOverviewRepository,
        private TermConditionRepository $termConditionRepository,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function getInformation(string $quantitySelectionId): QuantityInquiry
    {
        $quantitySelection = $this->quantitySelectionRepository->find(id: $quantitySelectionId);
        if (!$quantitySelection instanceof QuantitySelection) {
            throw new NotFoundException(message: 'Quantity Selection not found');
        }

        $quantityInquiryDto = new QuantityInquiry();
        $quantityInquiryDto->quantitySelectionId = $quantitySelection->getId();
        $quantityInquiryDto->title = sprintf('Ihr Vertragsabschluss - Paket %s', $quantitySelection->getModel()->getTitle());
        $quantityInquiryDto->information = '';
        $modelType = $quantitySelection->getModel();
        if ($modelType->getType() == ModelType::INQUIRY->value) {
            $quantityInquiryDto->title = sprintf('Formular Vertragsdaten - Paket %s', $quantitySelection->getModel()->getTitle());
            $quantityInquiryDto->information = 'Basierend auf der von Ihnen angegebenen Menge wurden Sie dem Premium-Paket zugeordnet. Nach Eingabe Ihrer Kontaktdaten wird sich unser Vertrieb mit einem individuellen Angebot bei Ihnen melden.';
        }

        return $quantityInquiryDto;
    }

    /**
     * @return Collection<QuantityInquiry>
     *
     * @throws \Exception
     */
    public function getCheckboxOptions(string $quantitySelectionId): Collection
    {
        $quantitySelection = $this->quantitySelectionRepository->find(id: $quantitySelectionId);
        if (!$quantitySelection instanceof QuantitySelection) {
            throw new NotFoundException(message: 'Quantity Selection not found');
        }

        $agreements = $this->agreementRepository->findBy(criteria: ['model' => $quantitySelection->getModel()->getId()]);

        $items = [];
        foreach ($agreements as $agreement) {
            $dto = new QuantityInquiry();
            $dto->id = $agreement->getId();
            $dto->display = $agreement->getName();
            $dto->required = $agreement->isRequired();
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    public function createQuantityInquiry(QuantityInquiry $quantityInquiryDto, string $quantitySelectionId): QuantityInquiry
    {
        $quantitySelection = $this->quantitySelectionRepository->find(id: $quantitySelectionId);
        if (!$quantitySelection instanceof QuantitySelection) {
            throw new NotFoundException(message: 'Quantity Selection not found');
        }

        $quantityInquiry = $this->mapDtoToEntity(quantityInquiryDto: $quantityInquiryDto, quantitySelection: $quantitySelection);
        $quantityInquiryDto->id = $quantityInquiry->getId();
        $prices = [];

        if (ProductType::BATTG->value === $quantitySelection->getModel()->getProductType()) {
            if (ModelType::ORDERING->value == $quantitySelection->getModel()->getType()) {
                $customerSubject = 'Eingangsbestätigung';
                $customerTemplate = 'email/BattG/customer_ordering.html.twig';
                $salesSubject = 'Neuer Kunde Batterierücknahme – '.$quantitySelection->getModel()->getTitle();
                $salesTemplate = 'email/BattG/sales_ordering.html.twig';
            } elseif (ModelType::INQUIRY->value == $quantitySelection->getModel()->getType()) {
                $customerSubject = 'Ihre Anfrage auf www.prezero.de';
                $customerTemplate = 'email/BattG/customer_inquiry.html.twig';
                $salesSubject = 'Lizenzshop Anfrage Großkunde';
                $salesTemplate = 'email/BattG/sales_inquiry.html.twig';
            } else {
                throw new NotFoundException(message: 'Model not found');
            }
        } elseif (ProductType::WEEE->value === $quantitySelection->getModel()->getProductType()) {
            if (ModelType::ORDERING->value == $quantitySelection->getModel()->getType()) {
                $customerSubject = 'Eingangsbestätigung';
                $customerTemplate = 'email/WEEE/customer_ordering.html.twig';
                $salesSubject = 'Neuer Kunde WEEE – '.$quantitySelection->getModel()->getTitle();
                $salesTemplate = 'email/WEEE/sales_ordering.html.twig';
            } elseif (ModelType::INQUIRY->value == $quantitySelection->getModel()->getType()) {
                $customerSubject = 'Ihre Anfrage auf www.prezero.de';
                $customerTemplate = 'email/WEEE/customer_inquiry.html.twig';
                $salesSubject = 'Lizenzshop Anfrage Großkunde';
                $salesTemplate = 'email/WEEE/sales_inquiry.html.twig';
            } else {
                throw new NotFoundException(message: 'Model not found');
            }

            if ($quantitySelection->getModel()->getClassification() === Classification::ADVANCED->value) {
                $prices[] = $quantitySelection->getAmount();
            }
        } else {
            throw new NotFoundException(message: 'ProductType not found');
        }

        if ([] === $prices) {
            $costOverview = $this->costOverviewRepository->findBy(criteria: ['model' => $quantitySelection->getModel()->getId()], orderBy: ['sequence' => 'ASC']);
            foreach ($costOverview as $costOverviewItem) {
                if (null !== $costOverviewItem->getPrice()) {
                    $prices[] = $costOverviewItem->getPrice();
                }
            }
        }

        $date = new \DateTimeImmutable(datetime: 'now');

        $link = '';

        /** @var TermCondition|null $termCondition */
        $termCondition = $quantityInquiry->getQuantitySelection()->getModel()->getTermConditions()[0] ?? null;
        if (null !== $termCondition) {
            $link = sprintf('%s/%s.pdf', $this->fileEndpoint, $termCondition->getId());
            if (null !== $termCondition->getUrl()) {
                $link = $termCondition->getUrl();
            }
        }

        $context = [
            'timestamp' => $date->format(format: 'd.m.Y H:i:s'),
            'start_date' => $date->format(format: '01.m.Y'),
            'end_date' => $date->format(format: '31.12.Y'),
            'firstname' => $quantityInquiry->getContactFirstname(),
            'lastname' => $quantityInquiry->getContactLastname(),
            'company_name' => $quantityInquiry->getCompanyName(),
            'address' => $quantityInquiry->getAddress(),
            'postal_code' => $quantityInquiry->getPostalCode(),
            'city' => $quantityInquiry->getCity(),
            'vat_id' => $quantityInquiry->getVatId(),
            'registration_number' => $quantityInquiry->getRegistrationNumber(),
            'product_type_registration_number' => $quantityInquiry->getProductTypeRegistrationNumber(),
            'phone_number' => $quantityInquiry->getContactPhoneNumber(),
            'contact_email' => $quantityInquiry->getContactEmail(),
            'einvoice_email' => $quantityInquiry->getEinvoiceEmail(),
            'industry_code' => $quantityInquiry->getIndustryCode(),
            'link' => $link,
            'selections' => $this->getSelectionForEmail(selections: $quantityInquiry->getQuantitySelection()->getQuantitySelectionContents()),
            'prices' => $prices,
        ];

        $salesEmails = [];
        if (null !== $quantitySelection->getModel()->getPrezeroContact()) {
            $prezeroContacts = explode(separator: ';', string: $quantitySelection->getModel()->getPrezeroContact());
            foreach ($prezeroContacts as $prezeroContact) {
                if ('' === $prezeroContact || '0' === $prezeroContact) {
                    continue;
                }
                $salesEmails[] = $prezeroContact;
            }
        }

        // Customer
        try {
            $this->mailer->send(
                emailTo: [$quantityInquiry->getContactEmail()],
                subject: $customerSubject,
                context: $context,
                htmlTemplate: $customerTemplate,
            );
            $quantityInquiry->setCustomerEmailSentAt(customerEmailSentAt: new \DateTimeImmutable(datetime: 'now'));
        } catch (TransportExceptionInterface) {
        }

        // Vertrieb
        try {
            $this->mailer->send(
                emailTo: $salesEmails,
                subject: $salesSubject,
                context: $context,
                htmlTemplate: $salesTemplate,
            );
            $quantityInquiry->setSalesEmailSentAt(salesEmailSentAt: new \DateTimeImmutable(datetime: 'now'));
        } catch (TransportExceptionInterface) {
        }

        $this->entityManager->persist($quantityInquiry);
        $this->entityManager->flush();

        return $quantityInquiryDto;
    }

    private function mapDtoToEntity(QuantityInquiry $quantityInquiryDto, QuantitySelection $quantitySelection): QuantityInquiryEntity
    {
        $quantityInquiry = new QuantityInquiryEntity();
        $quantityInquiry->setQuantitySelection(quantitySelection: $quantitySelection);

        // ContactPerson
        $quantityInquiry->setContactTitle(contactTitle: $quantityInquiryDto->company->contactPerson->title ?? '');
        $quantityInquiry->setContactFirstname(contactFirstname: $quantityInquiryDto->company->contactPerson->firstName);
        $quantityInquiry->setContactLastname(contactLastname: $quantityInquiryDto->company->contactPerson->lastName);
        $quantityInquiry->setContactEmail(contactEmail: $quantityInquiryDto->company->contactPerson->email);
        $quantityInquiry->setContactPhoneNumber(contactPhoneNumber: $quantityInquiryDto->company->contactPerson->phoneNumber);

        // Company
        $quantityInquiry->setCompanyName(companyName: $quantityInquiryDto->company->name);
        $quantityInquiry->setAddress(address: $quantityInquiryDto->company->address);
        $quantityInquiry->setAddressInfo(addressInfo: $quantityInquiryDto->company->addressInfo);
        $quantityInquiry->setPostalCode(postalCode: $quantityInquiryDto->company->postalCode);
        $quantityInquiry->setCity(city: $quantityInquiryDto->company->city);
        $quantityInquiry->setCountry(country: $quantityInquiryDto->company->countryId);
        $quantityInquiry->setVatId(vatId: $quantityInquiryDto->company->vatId);
        $quantityInquiry->setRegistrationNumber(registrationNumber: $quantityInquiryDto->company->registrationNumber);
        $quantityInquiry->setProductTypeRegistrationNumber(productTypeRegistrationNumber: $quantityInquiryDto->company->productTypeRegistrationNumber ?? '');
        $quantityInquiry->setIndustryCode(industryCode: $quantityInquiryDto->company->industryCode ?? '');
        $quantityInquiry->setEinvoiceEmail(einvoiceEmail: $quantityInquiryDto->company->eInvoiceEmail ?? '');

        $this->entityManager->persist($quantityInquiry);

        $terms = $this->termConditionRepository->findBy(criteria: ['model' => $quantitySelection->getModel()->getId(), 'active' => true]);
        foreach ($terms as $term) {
            $quantityInquiryTermCondition = new QuantityInquiryTermCondition();
            $quantityInquiryTermCondition->setQuantityInquiry(quantityInquiry: $quantityInquiry);
            $quantityInquiryTermCondition->setTermCondition(termCondition: $term);
            $this->entityManager->persist($quantityInquiryTermCondition);
        }

        foreach ($quantityInquiryDto->checkboxes as $checkbox) {
            $agreement = $this->agreementRepository->find(id: $checkbox->checkboxId);
            if (null === $agreement) {
                throw new NotFoundException(message: 'Agreement not found');
            }

            $quantityInquiryAgreement = new QuantityInquiryAgreement();
            $quantityInquiryAgreement->setQuantityInquiry(quantityInquiry: $quantityInquiry);
            $quantityInquiryAgreement->setAgreement(agreement: $agreement);
            $quantityInquiryAgreement->setSelected(selected: $checkbox->selected);
            $this->entityManager->persist($quantityInquiryAgreement);
        }

        return $quantityInquiry;
    }

    /**
     * @param \Doctrine\Common\Collections\Collection<int, QuantitySelectionContent> $selections
     *
     * @return array<mixed>
     */
    private function getSelectionForEmail(\Doctrine\Common\Collections\Collection $selections): array
    {
        $content = [];

        $row = null;
        $current = [];
        foreach ($selections as $selection) {
            if ($row != $selection->getRow()) {
                if ([] !== $current) {
                    $content[] = $current;
                    $current = [];
                }
                $row = $selection->getRow();
            }

            if (false === uuid_is_valid(uuid: $selection->getValue())) {
                $current['amount'] = $selection->getValue();
                continue;
            }

            $quantityOptionContent = $this->quantityOptionContentRepository->find(id: $selection->getValue());
            if (null === $quantityOptionContent) {
                continue;
            }

            $current['name'] = $quantityOptionContent->getName();

            if (null === $quantityOptionContent->getExternalId()) {
                continue;
            }

            $current['external_id'] = $quantityOptionContent->getExternalId();
            $current['external_description'] = $quantityOptionContent->getExternalDescription();
            $current['price'] = $quantityOptionContent->getPrice();
        }

        $content[] = $current;

        return $content;
    }
}
