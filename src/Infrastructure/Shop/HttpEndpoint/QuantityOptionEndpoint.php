<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Entity\Enum\ProductType;
use App\Domain\Entity\QuantityOptionContent as QuantityOptionContentEntity;
use App\Domain\Repository\QuantityOptionContentRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Shop\Resource\QuantityOption;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class QuantityOptionEndpoint
{
    public function __construct(
        private QuantityOptionContentRepository $quantityOptionContentRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
    ) {
    }

    /**
     * @return Collection<QuantityOption>
     *
     * @throws \Exception
     */
    public function getOptions(Request $request, string $productType, string $quantityOption): Collection
    {
        $selectedProductType = ProductType::tryFrom(value: $productType);
        if (null === $selectedProductType) {
            throw new NotFoundException(message: "Product type $productType not found");
        }

        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $quantityOptionContents = $this->quantityOptionContentRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, productType: $selectedProductType->value, quantityOption: $quantityOption);

        $items = array_map(
            callback: fn (QuantityOptionContentEntity $quantityOptionContent): QuantityOption => $this->mapQuantityOptionContentToDto(quantityOptionContent: $quantityOptionContent),
            array: $quantityOptionContents
        );

        usort(array: $items, callback: fn ($a, $b): int => strcmp(string1: $a->display, string2: $b->display));

        return new Collection(
            items: $items
        );
    }

    private function mapQuantityOptionContentToDto(QuantityOptionContentEntity $quantityOptionContent): QuantityOption
    {
        $dto = new QuantityOption();
        $dto->id = $quantityOptionContent->getId();
        $dto->display = $quantityOptionContent->getName();
        $dto->tooltip = $quantityOptionContent->getTooltip() ?? '';

        return $dto;
    }
}
