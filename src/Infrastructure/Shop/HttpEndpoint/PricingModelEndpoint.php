<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Entity\Enum\IncludedServicePage;
use App\Domain\Entity\Enum\ProductType;
use App\Domain\Entity\IncludedService as IncludedServiceEntity;
use App\Domain\Entity\Model as ModelEntity;
use App\Domain\Repository\IncludedServiceRepository;
use App\Domain\Repository\ModelRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Shop\Resource\Dto\IncludedService;
use App\Infrastructure\Shop\Resource\PricingModel;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class PricingModelEndpoint
{
    public function __construct(
        private ModelRepository $modelRepository,
        private IncludedServiceRepository $includedServiceRepository,
    ) {
    }

    /**
     * @return Collection<PricingModel>
     *
     * @throws \Exception
     */
    public function getPricingModels(string $productType): Collection
    {
        $selectedProductType = ProductType::tryFrom(value: $productType);
        if (null === $selectedProductType) {
            throw new NotFoundException(message: "Product type $productType not found");
        }

        $models = $this->modelRepository->findBy(criteria: ['productType' => $selectedProductType], orderBy: ['sequence' => 'ASC']);

        $items = array_map(
            callback: fn (ModelEntity $model): PricingModel => $this->mapModelToDto(model: $model),
            array: $models
        );

        return new Collection(
            items: $items
        );
    }

    private function mapModelToDto(ModelEntity $model): PricingModel
    {
        $dto = new PricingModel();
        $dto->title = sprintf('Paket "%s"', $model->getTitle());
        $dto->subtitle = $model->getSubtitle();
        $dto->price = $model->getPriceText() ?? '';
        $dto->price_subtitle = $model->getPriceSubtitle();

        if (null !== $model->getPrice()) {
            $dto->price = str_replace(search: '{{price}}', replace: (string) $model->getPrice(), subject: $dto->price);
        }
        if (null !== $model->getCurrency()) {
            $dto->price = str_replace(search: '{{currency}}', replace: $model->getCurrency(), subject: $dto->price);
        }

        $includedServices = $this->includedServiceRepository->findBy(criteria: ['model' => $model, 'included' => true, 'page' => IncludedServicePage::OVERVIEW->value], orderBy: ['sequence' => 'ASC']);

        $items = array_map(
            callback: fn (IncludedServiceEntity $includedService): IncludedService => $this->mapIncludedServiceToDto(includedService: $includedService),
            array: $includedServices
        );

        $dto->includedServices = $items;

        return $dto;
    }

    private function mapIncludedServiceToDto(IncludedServiceEntity $includedService): IncludedService
    {
        $dto = new IncludedService();
        $dto->text = $includedService->getText();

        return $dto;
    }
}
