<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Infrastructure\Shop\Resource\Country;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class CountryEndpoint
{
    /**
     * @return Collection<Country>
     */
    public function getOptions(string $quantitySelectionId): Collection
    {
        $items = [];

        $dto = new Country();
        $dto->id = 'DE';
        $dto->display = 'Deutschland';
        $items[] = $dto;

        return new Collection(
            items: $items
        );
    }
}
