<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Entity\QuantitySelection;
use App\Domain\Repository\QuantitySelectionRepository;
use App\Domain\Repository\TermConditionRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Shop\Resource\TermCondition;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class TermConditionEndpoint
{
    public function __construct(
        #[Autowire('%env(FILE_ENDPOINT)%')]
        private string $fileEndpoint,
        private QuantitySelectionRepository $quantitySelectionRepository,
        private TermConditionRepository $termConditionRepository,
    ) {
    }

    /**
     * @return Collection<TermCondition>
     */
    public function getOptions(string $quantitySelectionId): Collection
    {
        $quantitySelection = $this->quantitySelectionRepository->find(id: $quantitySelectionId);
        if (!$quantitySelection instanceof QuantitySelection) {
            throw new NotFoundException(message: 'Quantity Selection not found');
        }

        $terms = $this->termConditionRepository->findBy(criteria: ['model' => $quantitySelection->getModel()->getId(), 'active' => true]);

        $items = [];
        foreach ($terms as $term) {
            $dto = new TermCondition();
            $dto->id = $term->getId();
            $dto->display = $term->getName();

            $dto->link = sprintf('%s/%s.pdf', $this->fileEndpoint, $dto->id);
            if (null !== $term->getUrl()) {
                $dto->link = $term->getUrl();
            }

            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
