<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\HttpEndpoint;

use App\Domain\Service\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Service\ObjectStorage\ObjectStorageException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;

#[AsController]
readonly class FileEndpoint
{
    public function __construct(
        private ObjectRepositoryInterface $objectRepository,
        private LoggerInterface $logger,
    ) {
    }

    public function get(string $id): Response
    {
        try {
            $fileObject = $this->objectRepository->get($id);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not get a file from object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException();
        }

        if (null === $fileObject) {
            throw new NotFoundHttpException();
        }

        $disposition = HeaderUtils::makeDisposition(
            disposition: HeaderUtils::DISPOSITION_ATTACHMENT,
            filename: 'Nutzungsbedingungen.pdf',
        );

        return new Response(
            content: $fileObject->content,
            status: Response::HTTP_OK,
            headers: [
                'Content-Type' => $fileObject->mimeType,
                'Content-Disposition' => $disposition,
            ],
        );
    }
}
