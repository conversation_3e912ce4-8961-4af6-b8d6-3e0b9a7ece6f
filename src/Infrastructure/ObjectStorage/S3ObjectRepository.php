<?php

declare(strict_types=1);

namespace App\Infrastructure\ObjectStorage;

use App\Domain\Service\ObjectStorage\FileObject;
use App\Domain\Service\ObjectStorage\ObjectMetadata;
use App\Domain\Service\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Service\ObjectStorage\ObjectStorageException;
use Aws\Api\DateTimeResult;
use Aws\S3\Exception\S3Exception;
use Aws\S3\S3Client;
use Psr\Http\Message\StreamInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class S3ObjectRepository implements ObjectRepositoryInterface
{
    public function __construct(
        #[Autowire('%app.storage.bucket_name%')]
        private string $bucketName,
        #[Autowire('@s3_client')]
        private S3Client $s3Client,
        // private TenantContext $tenantContext,
        private LoggerInterface $logger,
    ) {
    }

    public function store(FileObject $object): void
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'putObject',
                'path' => $object->identifier,
            ]);

            $this->s3Client->putObject([
                'Bucket' => $this->bucketName,
                'Key' => $this->setTenantIdentifier(identifier: $object->identifier),
                'Body' => $object->content,
                'ContentType' => $object->mimeType,
                'Metadata' => $object->objectMetadata?->toArray() ?? [],
            ]);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }

    public function get(string $identifier): ?FileObject
    {
        $this->ensureBucketExists();
        $identifier = $this->setTenantIdentifier(identifier: $identifier);

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'getObject',
                'path' => $identifier,
            ]);

            /**
             * @var array{
             *     Metadata: array<string, string>,
             *     Body: ?StreamInterface,
             *     ContentType?: string,
             *     LastModified: DateTimeResult,
             * } $result
             */
            $result = $this->s3Client->getObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return null;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        $userIdentifier = null;
        $tenantIdentifier = null;

        $miscMetadata = [];
        foreach ($result['Metadata'] as $key => $value) {
            match ($key) {
                'user_identifier' => $userIdentifier = $value,
                'tenant_identifier' => $tenantIdentifier = $value,
                default => $miscMetadata[$key] = $value,
            };
        }

        return new FileObject(
            identifier: $identifier,
            content: $result['Body'] ? $result['Body']->getContents() : '',
            lastModified: $result['LastModified'],
            mimeType: $result['ContentType'] ?? 'application/octet-stream',
            objectMetadata: new ObjectMetadata(
                tenantIdentifier: $tenantIdentifier,
                userIdentifier: $userIdentifier,
                misc: $miscMetadata
            ),
        );
    }

    public function exists(string $identifier): bool
    {
        $this->ensureBucketExists();
        $identifier = $this->setTenantIdentifier(identifier: $identifier);

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'headObject',
                'path' => $identifier,
            ]);

            $this->s3Client->headObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return false;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        return true;
    }

    private function setTenantIdentifier(string $identifier): string
    {
        // $tenantIdentifier = $this->tenantContext->getTenantIdentifier();
        // if (!str_starts_with($identifier, $tenantIdentifier.'/')) {
        //    return $tenantIdentifier.'/'.$identifier;
        // }

        return $identifier;
    }

    public function metadata(string $identifier): ?ObjectMetadata
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'headObject',
                'path' => $identifier,
            ]);

            /**
             * @var array{
             *     Metadata: array<string, string>,
             *     ContentType?: string,
             *     LastModified: DateTimeResult,
             * } $result
             */
            $result = $this->s3Client->headObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return null;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        $userIdentifier = null;
        $tenantIdentifier = null;

        $miscMetadata = [];
        foreach ($result['Metadata'] as $key => $value) {
            match ($key) {
                'user_identifier' => $userIdentifier = $value,
                'tenant_identifier' => $tenantIdentifier = $value,
                default => $miscMetadata[$key] = $value,
            };
        }

        return new ObjectMetadata(
            tenantIdentifier: $tenantIdentifier,
            userIdentifier: $userIdentifier,
            misc: $miscMetadata
        );
    }

    public function delete(string $identifier): void
    {
        $this->ensureBucketExists();

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'deleteObject',
                'path' => $identifier,
            ]);

            $this->s3Client->deleteObject([
                'Bucket' => $this->bucketName,
                'Key' => $identifier,
            ]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 'NoSuchKey' === $e->getAwsErrorCode()) {
                return;
            }

            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }

    /**
     * @throws ObjectStorageException
     */
    private function ensureBucketExists(): void
    {
        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'listBuckets',
            ]);

            $buckets = $this->s3Client->listBuckets();
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }

        assert(assertion: is_array(value: $buckets['Buckets']));

        /** @var array{Name: string} $bucket */
        foreach ($buckets['Buckets'] as $bucket) {
            if ($bucket['Name'] === $this->bucketName) {
                return;
            }
        }

        try {
            $this->logger->info('S3-access-operation', [
                'operation' => 'createBucket',
            ]);

            $this->s3Client->createBucket([
                'Bucket' => $this->bucketName,
            ]);
        } catch (\Exception $e) {
            throw new ObjectStorageException(message: $e->getMessage(), previous: $e);
        }
    }
}
