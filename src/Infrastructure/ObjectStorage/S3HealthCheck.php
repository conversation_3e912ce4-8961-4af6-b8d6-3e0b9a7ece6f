<?php

declare(strict_types=1);

namespace App\Infrastructure\ObjectStorage;

use Aws\S3\S3Client;
use <PERSON><PERSON>\Diagnostics\Check\CheckInterface;
use <PERSON><PERSON>\Diagnostics\Result\Failure;
use <PERSON><PERSON>\Diagnostics\Result\ResultInterface;
use <PERSON><PERSON>\Diagnostics\Result\Success;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class S3HealthCheck implements CheckInterface
{
    public function __construct(
        #[Autowire('@s3_client')]
        private S3Client $s3Client,
    ) {
    }

    public function check(): ResultInterface
    {
        try {
            $buckets = $this->s3Client->listBuckets();
        } catch (\Throwable) {
            return new Failure(message: 'S3 connection is not OK');
        }

        if (is_array(value: $buckets['Buckets'])) {
            return new Success(message: 'S3 connection is OK');
        }

        return new Failure(message: 'S3 connection is not OK');
    }

    public function getLabel(): string
    {
        return 'S3 Connection';
    }
}
