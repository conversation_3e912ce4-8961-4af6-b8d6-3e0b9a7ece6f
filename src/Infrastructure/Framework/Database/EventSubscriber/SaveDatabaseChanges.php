<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Database\EventSubscriber;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(event: KernelEvents::RESPONSE, method: 'onKernelResponse', priority: 3)]
readonly class SaveDatabaseChanges
{
    public function __construct(
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function onKernelResponse(ResponseEvent $responseEvent): void
    {
        $response = $responseEvent->getResponse();

        if ($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
            $this->entityManager->flush();
        }
    }
}
