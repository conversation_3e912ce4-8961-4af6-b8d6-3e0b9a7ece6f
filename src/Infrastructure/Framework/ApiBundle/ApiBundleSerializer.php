<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\ApiBundle;

use App\Infrastructure\Framework\Serializer\SerializerException;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\Serializer\SerializerInterface;

class ApiBundleSerializer implements SerializerInterface
{
    public function __construct(
        private readonly \App\Infrastructure\Framework\Serializer\SerializerInterface $serializer,
    ) {
    }

    public function serialize(mixed $data, string $format, array $context = []): string
    {
        try {
            return $this->serializer->serialize($data, 'json', context: $context);
        } catch (SerializerException $e) {
            throw new ApiBundleException(message: $e->getMessage(), code: $e->getCode(), previous: $e);
        }
    }

    public function deserialize(mixed $data, string $type, string $format, array $context = []): mixed
    {
        try {
            return $this->serializer->deserialize($data, $type, 'json', context: $context);
        } catch (SerializerException $e) {
            throw new ApiBundleException(message: $e->getMessage(), code: $e->getCode(), previous: $e);
        }
    }

    public function denormalize(mixed $data, ?string $type, array $context = []): mixed
    {
        try {
            return $this->serializer->denormalize($data, $type, context: $context);
        } catch (SerializerException $e) {
            throw new ApiBundleException(message: $e->getMessage(), code: $e->getCode(), previous: $e);
        }
    }

    public function normalize(mixed $data, array $context): array|string|int|float|bool|null
    {
        try {
            return $this->serializer->normalize($data, context: $context);
        } catch (SerializerException $e) {
            throw new ApiBundleException(message: $e->getMessage(), code: $e->getCode(), previous: $e);
        }
    }
}
