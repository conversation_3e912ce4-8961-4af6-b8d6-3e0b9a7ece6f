<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\ApiBundle;

use App\Exception\BadRequestException;
use App\Exception\NotFoundException;
use App\Exception\ValidationException;
use PreZero\ApiBundle\JsonResponse;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\Validator\ConstraintViolationInterface;

#[AsEventListener]
readonly class CustomExceptionFormatter
{
    public function __invoke(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        if ($exception instanceof ValidationException) {
            $event->setResponse(
                response: new JsonResponse(
                    data: [
                        'type' => 'validation_error',
                        'title' => 'Validation error',
                        'detail' => implode(
                            separator: ';',
                            array: array_map(
                                callback: static fn (ConstraintViolationInterface $violation): string => (string) $violation->getMessage(),
                                array: $exception->getConstraintViolation()
                            )
                        ),
                    ],
                    status: 0 === $exception->getCode() ? 400 : $exception->getCode(),
                )
            );

            return;
        }

        if ($exception instanceof BadRequestException) {
            $event->setResponse(
                response: new JsonResponse(
                    data: [
                        'type' => 'bad_request_data',
                        'title' => 'request-data could not be processed',
                        'detail' => $exception->getMessage(),
                    ],
                    status: 0 === $exception->getCode() ? 400 : $exception->getCode(),
                )
            );
        }

        if ($exception instanceof NotFoundException) {
            $event->setResponse(
                response: new JsonResponse(
                    data: [
                        'type' => 'not_found',
                        'title' => 'data not found',
                        'detail' => $exception->getMessage(),
                    ],
                    status: 0 === $exception->getCode() ? 404 : $exception->getCode(),
                )
            );
        }
    }
}
