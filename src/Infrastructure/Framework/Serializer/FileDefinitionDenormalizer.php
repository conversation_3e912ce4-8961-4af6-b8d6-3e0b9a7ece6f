<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use App\Domain\ValueObject\FileDefinition;
use V<PERSON>ss\Serializer\Denormalizer;
use Vuryss\Serializer\Denormalizer\DenormalizerInterface;
use Vuryss\Serializer\Denormalizer\ObjectDenormalizer;
use Vuryss\Serializer\Exception\DeserializationImpossibleException;
use Vuryss\Serializer\Metadata\BuiltInType;
use Vuryss\Serializer\Metadata\DataType;
use Vuryss\Serializer\Path;

readonly class FileDefinitionDenormalizer implements DenormalizerInterface
{
    public function denormalize(
        mixed $data,
        DataType $type,
        Denormalizer $denormalizer,
        Path $path,
        array $context = [],
    ): mixed {
        if (!is_array(value: $data)) {
            throw new DeserializationImpossibleException(message: 'Cannot denormalize into FileDefinition because data is not an object');
        }

        $objectDenormalizer = new ObjectDenormalizer();
        $fileDefinition = $objectDenormalizer->denormalize(data: $data, type: $type, denormalizer: $denormalizer, path: $path);

        assert(assertion: $fileDefinition instanceof FileDefinition);

        $fileName = trim(string: $fileDefinition->file, characters: '/');

        return new FileDefinition(
            file: $fileName,
            name: $fileDefinition->name,
            label: $fileDefinition->label,
        );
    }

    public function supportsDenormalization(mixed $data, DataType $type): bool
    {
        return BuiltInType::OBJECT === $type->type && FileDefinition::class === $type->className;
    }
}
