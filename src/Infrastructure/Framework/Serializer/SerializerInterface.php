<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use App\Infrastructure\Framework\Serializer\Exception\DeserializationException;
use Vuryss\Serializer\Context;

/**
 * @phpstan-import-type ContextOptions from Context
 */
interface SerializerInterface
{
    /**
     * Serializes data in the appropriate format.
     *
     * @phpstan-param mixed $data
     * @phpstan-param 'json' $format
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @throws SerializerException
     */
    public function serialize(mixed $data, string $format, array $context = []): string;

    /**
     * Deserializes data into the given type.
     *
     * @template TObject of object
     * @template TType of string|class-string<TObject>|class-string
     *
     * @phpstan-param mixed $data
     * @phpstan-param TType $type
     * @phpstan-param 'json' $format
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return ($type is class-string<TObject> ? TObject : ($type is class-string ? object : mixed))
     *
     * @psalm-return (TType is class-string<TObject> ? TObject : (TType is class-string ? object : mixed))
     *
     * @throws DeserializationException
     */
    public function deserialize(mixed $data, string $type, string $format, array $context = []): mixed;

    /**
     * Denormalized data into the given type.
     *
     * @template TObject of object
     * @template TType of string|class-string<TObject>|class-string
     *
     * @phpstan-param mixed $data
     * @phpstan-param TType $type
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return ($type is class-string<TObject> ? TObject : ($type is class-string ? object : mixed))
     *
     * @psalm-return (TType is class-string<TObject> ? TObject : (TType is class-string ? object : mixed))
     *
     * @throws DeserializationException
     */
    public function denormalize(mixed $data, ?string $type, array $context = []): mixed;

    /**
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return array<mixed>|string|int|float|bool|null
     *
     * @throws SerializerException
     */
    public function normalize(mixed $data, array $context): array|string|int|float|bool|null;

    /**
     * Deserializes data into the given object.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return T
     *
     * @throws DeserializationException
     */
    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object;

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-param ContextOptions $context Options normalizers have access to
     *
     * @phpstan-return array<T>
     *
     * @throws DeserializationException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array;
}
