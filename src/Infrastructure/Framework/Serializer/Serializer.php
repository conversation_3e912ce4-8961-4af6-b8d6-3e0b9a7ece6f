<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Serializer;

use App\Infrastructure\Framework\Serializer\Exception\DeserializationException;
use App\Infrastructure\Framework\Serializer\Exception\SerializationException;
use Psr\Cache\CacheItemPoolInterface;
use Vuryss\Serializer\Context;
use V<PERSON>ss\Serializer\Denormalizer\ArrayDenormalizer;
use Vuryss\Serializer\Denormalizer\BasicTypesDenormalizer;
use Vuryss\Serializer\Denormalizer\DateTimeDenormalizer;
use Vuryss\Serializer\Denormalizer\EnumDenormalizer;
use Vuryss\Serializer\Denormalizer\InterfaceDenormalizer;
use Vuryss\Serializer\Denormalizer\ObjectDenormalizer;
use Vuryss\Serializer\ExceptionInterface;
use Vuryss\Serializer\Metadata\CachedMetadataExtractor;
use Vuryss\Serializer\Metadata\MetadataExtractor;

readonly class Serializer implements SerializerInterface
{
    private \Vuryss\Serializer\SerializerInterface $vuryssSerializer;

    public function __construct(
        private CacheItemPoolInterface $serializerCache,
        private FileDefinitionDenormalizer $fileDefinitionDenormalizer,
    ) {
        $this->vuryssSerializer = new \Vuryss\Serializer\Serializer(
            denormalizers: [
                new BasicTypesDenormalizer(),
                new ArrayDenormalizer(),
                new EnumDenormalizer(),
                new DateTimeDenormalizer(),
                $this->fileDefinitionDenormalizer,
                new ObjectDenormalizer(),
                new InterfaceDenormalizer(),
            ],
            metadataExtractor: new CachedMetadataExtractor(
                metadataExtractor: new MetadataExtractor(),
                externalCache: $this->serializerCache
            ),
            context: [
                Context::DATETIME_FORMAT => \DateTimeInterface::RFC3339_EXTENDED,
                Context::SKIP_NULL_VALUES => true,
            ],
        );
    }

    public function serialize(mixed $data, string $format, array $context = []): string
    {
        try {
            return $this->vuryssSerializer->serialize($data, format: $format, context: $context);
        } catch (ExceptionInterface $e) {
            throw new SerializationException($e->getMessage(), previous: $e);
        }
    }

    public function deserialize(mixed $data, string $type, string $format, array $context = []): mixed
    {
        try {
            return $this->vuryssSerializer->deserialize($data, $type, $format, context: $context);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    public function denormalize(mixed $data, ?string $type, array $context = []): mixed
    {
        try {
            return $this->vuryssSerializer->denormalize($data, $type, context: $context);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    public function normalize(mixed $data, array $context): array|string|int|float|bool|null
    {
        try {
            return $this->vuryssSerializer->normalize($data, context: $context);
        } catch (ExceptionInterface $e) {
            throw new SerializationException($e->getMessage(), previous: $e);
        }
    }

    public function deserializeIntoObject(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): object {
        try {
            return $this->vuryssSerializer->deserialize($data, $className, 'json', context: $context);
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }

    /**
     * Deserializes data into an array of objects.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @phpstan-return array<T>
     *
     * @throws DeserializationException
     */
    public function deserializeIntoArrayOfObjects(
        string $data,
        string $className,
        string $format = 'json',
        array $context = [],
    ): array {
        try {
            /** @var array<T> $array */
            $array = $this->vuryssSerializer->deserialize($data, $className.'[]', 'json', context: $context);

            return $array;
        } catch (ExceptionInterface $e) {
            throw new DeserializationException($e->getMessage(), previous: $e);
        }
    }
}
