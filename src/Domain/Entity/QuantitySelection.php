<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantitySelectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantitySelectionRepository::class)]
class QuantitySelection implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(nullable: true)]
    private ?float $amount = null;

    #[ORM\OneToOne(mappedBy: 'quantitySelection', cascade: ['persist', 'remove'])]
    private ?QuantityInquiry $quantityInquiry = null;

    #[ORM\ManyToOne(inversedBy: 'quantitySelections')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    /**
     * @var Collection<int, QuantitySelectionContent>
     */
    #[ORM\OneToMany(targetEntity: QuantitySelectionContent::class, mappedBy: 'quantitySelection')]
    private Collection $quantitySelectionContents;

    public function __construct()
    {
        $this->init();
        $this->quantitySelectionContents = new ArrayCollection();
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(?float $amount): static
    {
        $this->amount = $amount;

        return $this;
    }

    public function getQuantityInquiry(): ?QuantityInquiry
    {
        return $this->quantityInquiry;
    }

    public function setQuantityInquiry(QuantityInquiry $quantityInquiry): static
    {
        // set the owning side of the relation if necessary
        if ($quantityInquiry->getQuantitySelection() !== $this) {
            $quantityInquiry->setQuantitySelection(quantitySelection: $this);
        }

        $this->quantityInquiry = $quantityInquiry;

        return $this;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    /**
     * @return Collection<int, QuantitySelectionContent>
     */
    public function getQuantitySelectionContents(): Collection
    {
        return $this->quantitySelectionContents;
    }

    public function addQuantitySelectionContent(QuantitySelectionContent $quantitySelectionContent): static
    {
        if (!$this->quantitySelectionContents->contains($quantitySelectionContent)) {
            $this->quantitySelectionContents->add($quantitySelectionContent);
            $quantitySelectionContent->setQuantitySelection(quantitySelection: $this);
        }

        return $this;
    }
}
