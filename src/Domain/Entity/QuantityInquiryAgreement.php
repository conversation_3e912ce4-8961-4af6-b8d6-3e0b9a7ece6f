<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantityInquiryAgreementRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantityInquiryAgreementRepository::class)]
class QuantityInquiryAgreement implements EntityInterface
{
    use CommonTrait;
    #[ORM\Column]
    private bool $selected;

    #[ORM\ManyToOne(inversedBy: 'quantityInquiryAgreements')]
    #[ORM\JoinColumn(nullable: false)]
    private QuantityInquiry $quantityInquiry;

    #[ORM\ManyToOne(inversedBy: 'quantityInquiryAgreements')]
    #[ORM\JoinColumn(nullable: false)]
    private Agreement $agreement;

    public function __construct()
    {
        $this->init();
    }

    public function isSelected(): bool
    {
        return $this->selected;
    }

    public function setSelected(bool $selected): static
    {
        $this->selected = $selected;

        return $this;
    }

    public function getQuantityInquiry(): QuantityInquiry
    {
        return $this->quantityInquiry;
    }

    public function setQuantityInquiry(QuantityInquiry $quantityInquiry): static
    {
        $this->quantityInquiry = $quantityInquiry;

        return $this;
    }

    public function getAgreement(): Agreement
    {
        return $this->agreement;
    }

    public function setAgreement(Agreement $agreement): static
    {
        $this->agreement = $agreement;

        return $this;
    }
}
