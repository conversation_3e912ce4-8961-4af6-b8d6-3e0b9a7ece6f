<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\IncludedServiceRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: IncludedServiceRepository::class)]
class IncludedService implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(length: 200)]
    private string $text;

    #[ORM\Column(type: Types::INTEGER)]
    private int $sequence;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $page = null;

    #[ORM\Column(nullable: true)]
    private ?bool $included = null;

    #[ORM\ManyToOne(inversedBy: 'includedServices')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    public function __construct()
    {
        $this->init();
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(string $text): static
    {
        $this->text = $text;

        return $this;
    }

    public function getSequence(): int
    {
        return $this->sequence;
    }

    public function setSequence(int $sequence): static
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function getPage(): ?string
    {
        return $this->page;
    }

    public function setPage(?string $page): static
    {
        $this->page = $page;

        return $this;
    }

    public function getIncluded(): ?bool
    {
        return $this->included;
    }

    public function setIncluded(?bool $included): static
    {
        $this->included = $included;

        return $this;
    }
}
