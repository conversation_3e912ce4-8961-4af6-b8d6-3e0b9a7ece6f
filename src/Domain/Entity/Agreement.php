<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\AgreementRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: AgreementRepository::class)]
class Agreement implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(length: 255)]
    private string $name;

    #[ORM\Column]
    private bool $required;

    #[ORM\ManyToOne(inversedBy: 'agreements')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    /**
     * @var Collection<int, QuantityInquiryAgreement>
     */
    #[ORM\OneToMany(targetEntity: QuantityInquiryAgreement::class, mappedBy: 'agreement')]
    private Collection $quantityInquiryAgreements;

    public function __construct()
    {
        $this->init();
        $this->quantityInquiryAgreements = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function isRequired(): bool
    {
        return $this->required;
    }

    public function setRequired(bool $required): static
    {
        $this->required = $required;

        return $this;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    /**
     * @return Collection<int, QuantityInquiryAgreement>
     */
    public function getQuantityInquiryAgreements(): Collection
    {
        return $this->quantityInquiryAgreements;
    }

    public function addQuantityInquiryAgreement(QuantityInquiryAgreement $quantityInquiryAgreement): static
    {
        if (!$this->quantityInquiryAgreements->contains($quantityInquiryAgreement)) {
            $this->quantityInquiryAgreements->add($quantityInquiryAgreement);
            $quantityInquiryAgreement->setAgreement(agreement: $this);
        }

        return $this;
    }
}
