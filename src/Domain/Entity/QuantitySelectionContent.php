<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantitySelectionContentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantitySelectionContentRepository::class)]
class QuantitySelectionContent implements EntityInterface
{
    use CommonTrait;

    #[ORM\ManyToOne(inversedBy: 'quantitySelectionContents')]
    #[ORM\JoinColumn(nullable: false)]
    private QuantitySelection $quantitySelection;

    #[ORM\Column]
    private int $row;

    #[ORM\ManyToOne(inversedBy: 'quantitySelectionContents')]
    #[ORM\JoinColumn(nullable: false)]
    private QuantityOption $quantityOption;

    #[ORM\Column(length: 255)]
    private string $value;

    public function __construct()
    {
        $this->init();
    }

    public function getQuantitySelection(): QuantitySelection
    {
        return $this->quantitySelection;
    }

    public function setQuantitySelection(QuantitySelection $quantitySelection): static
    {
        $this->quantitySelection = $quantitySelection;

        return $this;
    }

    public function getRow(): int
    {
        return $this->row;
    }

    public function setRow(int $row): static
    {
        $this->row = $row;

        return $this;
    }

    public function getQuantityOption(): QuantityOption
    {
        return $this->quantityOption;
    }

    public function setQuantityOption(QuantityOption $quantityOption): static
    {
        $this->quantityOption = $quantityOption;

        return $this;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function setValue(string $value): static
    {
        $this->value = $value;

        return $this;
    }
}
