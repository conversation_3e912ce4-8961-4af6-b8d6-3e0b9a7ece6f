<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantityInquiryTermConditionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantityInquiryTermConditionRepository::class)]
class QuantityInquiryTermCondition implements EntityInterface
{
    use CommonTrait;

    #[ORM\ManyToOne(inversedBy: 'quantityInquiryTermConditions')]
    #[ORM\JoinColumn(nullable: false)]
    private QuantityInquiry $quantityInquiry;

    #[ORM\ManyToOne(inversedBy: 'quantityInquiryTermConditions')]
    #[ORM\JoinColumn(nullable: false)]
    private TermCondition $termCondition;

    public function __construct()
    {
        $this->init();
    }

    public function getQuantityInquiry(): QuantityInquiry
    {
        return $this->quantityInquiry;
    }

    public function setQuantityInquiry(QuantityInquiry $quantityInquiry): static
    {
        $this->quantityInquiry = $quantityInquiry;

        return $this;
    }

    public function getTermCondition(): TermCondition
    {
        return $this->termCondition;
    }

    public function setTermCondition(TermCondition $termCondition): static
    {
        $this->termCondition = $termCondition;

        return $this;
    }
}
