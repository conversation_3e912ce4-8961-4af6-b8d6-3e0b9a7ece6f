<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\TermConditionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TermConditionRepository::class)]
class TermCondition implements EntityInterface
{
    use CommonTrait;

    #[ORM\ManyToOne(inversedBy: 'termConditions')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    #[ORM\Column(length: 100)]
    private string $name;

    #[ORM\Column(length: 100)]
    private string $description;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $url = null;

    #[ORM\Column(nullable: true)]
    private ?bool $active = null;

    /**
     * @var Collection<int, QuantityInquiryTermCondition>
     */
    #[ORM\OneToMany(targetEntity: QuantityInquiryTermCondition::class, mappedBy: 'termCondition')]
    private Collection $quantityInquiryTermConditions;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->quantityInquiryTermConditions = new ArrayCollection();
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): static
    {
        $this->url = $url;

        return $this;
    }

    public function getActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(?bool $active): static
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @return Collection<int, QuantityInquiryTermCondition>
     */
    public function getQuantityInquiryTermConditions(): Collection
    {
        return $this->quantityInquiryTermConditions;
    }

    public function addQuantityInquiryTermCondition(QuantityInquiryTermCondition $quantityInquiryTermCondition): static
    {
        if (!$this->quantityInquiryTermConditions->contains($quantityInquiryTermCondition)) {
            $this->quantityInquiryTermConditions->add($quantityInquiryTermCondition);
            $quantityInquiryTermCondition->setTermCondition(termCondition: $this);
        }

        return $this;
    }
}
