<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use App\Domain\Entity\Enum\SystemUser;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Uid\Uuid;

trait CommonTrait
{
    #[ORM\Id]
    #[ORM\Column(type: Types::GUID)]
    private string $id;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: false)]
    private \DateTimeImmutable $createdAt;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $createdBy;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: false)]
    private \DateTimeImmutable $modifiedAt;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $modifiedBy;

    private function init(?string $id = null): void
    {
        $this->id = $id ?? Uuid::v4()->toRfc4122();
        $this->createdAt = new \DateTimeImmutable();
        $this->createdBy = SystemUser::DEFAULT->value;
        $this->modifiedAt = new \DateTimeImmutable();
        $this->modifiedBy = SystemUser::DEFAULT->value;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getCreatedBy(): string
    {
        return $this->createdBy;
    }

    public function setCreatedBy(string $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getModifiedAt(): \DateTimeImmutable
    {
        return $this->modifiedAt;
    }

    public function setModifiedAt(\DateTimeInterface $modifiedAt): self
    {
        $this->modifiedAt = \DateTimeImmutable::createFromInterface(object: $modifiedAt);

        return $this;
    }

    public function getModifiedBy(): string
    {
        return $this->modifiedBy;
    }

    public function setModifiedBy(string $modifiedBy): self
    {
        $this->modifiedBy = $modifiedBy;

        return $this;
    }
}
