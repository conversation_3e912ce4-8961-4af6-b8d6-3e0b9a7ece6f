<?php

declare(strict_types=1);

namespace App\Domain\Entity\Interface;

interface EntityInterface
{
    public function getId(): string;

    public function getCreatedAt(): \DateTimeImmutable;

    public function getCreatedBy(): ?string;

    public function setCreatedBy(string $createdByUserId): self;

    public function getModifiedAt(): ?\DateTimeImmutable;

    public function setModifiedAt(\DateTimeImmutable $modifiedAt): self;

    public function getModifiedBy(): ?string;

    public function setModifiedBy(string $modifiedByUserId): self;
}
