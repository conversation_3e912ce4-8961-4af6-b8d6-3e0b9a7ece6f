<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\CostOverviewTextRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CostOverviewTextRepository::class)]
class CostOverviewText implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(length: 250)]
    private string $text;

    #[ORM\Column(type: Types::INTEGER)]
    private int $sequence;

    #[ORM\ManyToOne(inversedBy: 'costOverviewTexts')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    public function __construct()
    {
        $this->init();
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(string $text): static
    {
        $this->text = $text;

        return $this;
    }

    public function getSequence(): int
    {
        return $this->sequence;
    }

    public function setSequence(int $sequence): static
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }
}
