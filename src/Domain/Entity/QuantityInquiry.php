<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantityInquiryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantityInquiryRepository::class)]
class QuantityInquiry implements EntityInterface
{
    use CommonTrait;

    #[ORM\OneToOne(inversedBy: 'quantityInquiry', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private QuantitySelection $quantitySelection;

    /**
     * @var Collection<int, QuantityInquiryAgreement>
     */
    #[ORM\OneToMany(targetEntity: QuantityInquiryAgreement::class, mappedBy: 'quantityInquiry')]
    private Collection $quantityInquiryAgreements;

    #[ORM\Column(length: 5)]
    private string $contactTitle;

    #[ORM\Column(length: 50)]
    private string $contactFirstname;

    #[ORM\Column(length: 50)]
    private string $contactLastname;

    #[ORM\Column(length: 50)]
    private string $contactPhoneNumber;

    #[ORM\Column(length: 254)]
    private string $contactEmail;

    #[ORM\Column(length: 100)]
    private string $companyName;

    #[ORM\Column(length: 100)]
    private string $address;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $addressInfo = null;

    #[ORM\Column(length: 25)]
    private string $postalCode;

    #[ORM\Column(length: 50)]
    private string $city;

    #[ORM\Column(length: 3)]
    private string $country;

    #[ORM\Column(length: 20)]
    private string $vatId;

    #[ORM\Column(length: 20)]
    private string $registrationNumber;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $productTypeRegistrationNumber = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $industryCode = null;

    #[ORM\Column(length: 254)]
    private string $einvoiceEmail;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $customerEmailSentAt = null;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $salesEmailSentAt = null;

    /**
     * @var Collection<int, QuantityInquiryTermCondition>
     */
    #[ORM\OneToMany(targetEntity: QuantityInquiryTermCondition::class, mappedBy: 'quantityInquiry')]
    private Collection $quantityInquiryTermConditions;

    public function __construct()
    {
        $this->init();
        $this->quantityInquiryAgreements = new ArrayCollection();
        $this->quantityInquiryTermConditions = new ArrayCollection();
    }

    public function getQuantitySelection(): QuantitySelection
    {
        return $this->quantitySelection;
    }

    public function setQuantitySelection(QuantitySelection $quantitySelection): static
    {
        $this->quantitySelection = $quantitySelection;

        return $this;
    }

    /**
     * @return Collection<int, QuantityInquiryAgreement>
     */
    public function getQuantityInquiryAgreements(): Collection
    {
        return $this->quantityInquiryAgreements;
    }

    public function addQuantityInquiryAgreement(QuantityInquiryAgreement $quantityInquiryAgreement): static
    {
        if (!$this->quantityInquiryAgreements->contains($quantityInquiryAgreement)) {
            $this->quantityInquiryAgreements->add($quantityInquiryAgreement);
            $quantityInquiryAgreement->setQuantityInquiry(quantityInquiry: $this);
        }

        return $this;
    }

    public function getContactTitle(): string
    {
        return $this->contactTitle;
    }

    public function setContactTitle(string $contactTitle): static
    {
        $this->contactTitle = $contactTitle;

        return $this;
    }

    public function getContactFirstname(): string
    {
        return $this->contactFirstname;
    }

    public function setContactFirstname(string $contactFirstname): static
    {
        $this->contactFirstname = $contactFirstname;

        return $this;
    }

    public function getContactLastname(): string
    {
        return $this->contactLastname;
    }

    public function setContactLastname(string $contactLastname): static
    {
        $this->contactLastname = $contactLastname;

        return $this;
    }

    public function getContactPhoneNumber(): string
    {
        return $this->contactPhoneNumber;
    }

    public function setContactPhoneNumber(string $contactPhoneNumber): static
    {
        $this->contactPhoneNumber = $contactPhoneNumber;

        return $this;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function setCompanyName(string $companyName): static
    {
        $this->companyName = $companyName;

        return $this;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): static
    {
        $this->address = $address;

        return $this;
    }

    public function getAddressInfo(): ?string
    {
        return $this->addressInfo;
    }

    public function setAddressInfo(?string $addressInfo): static
    {
        $this->addressInfo = $addressInfo;

        return $this;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function setPostalCode(string $postalCode): static
    {
        $this->postalCode = $postalCode;

        return $this;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): static
    {
        $this->city = $city;

        return $this;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getVatId(): string
    {
        return $this->vatId;
    }

    public function setVatId(string $vatId): static
    {
        $this->vatId = $vatId;

        return $this;
    }

    public function getRegistrationNumber(): string
    {
        return $this->registrationNumber;
    }

    public function setRegistrationNumber(string $registrationNumber): static
    {
        $this->registrationNumber = $registrationNumber;

        return $this;
    }

    public function getProductTypeRegistrationNumber(): ?string
    {
        return $this->productTypeRegistrationNumber;
    }

    public function setProductTypeRegistrationNumber(?string $productTypeRegistrationNumber): static
    {
        $this->productTypeRegistrationNumber = $productTypeRegistrationNumber;

        return $this;
    }

    public function getIndustryCode(): ?string
    {
        return $this->industryCode;
    }

    public function setIndustryCode(?string $industryCode): static
    {
        $this->industryCode = $industryCode;

        return $this;
    }

    public function getContactEmail(): string
    {
        return $this->contactEmail;
    }

    public function setContactEmail(string $contactEmail): static
    {
        $this->contactEmail = $contactEmail;

        return $this;
    }

    public function getEinvoiceEmail(): string
    {
        return $this->einvoiceEmail;
    }

    public function setEinvoiceEmail(string $einvoiceEmail): static
    {
        $this->einvoiceEmail = $einvoiceEmail;

        return $this;
    }

    /**
     * @return Collection<int, QuantityInquiryTermCondition>
     */
    public function getQuantityInquiryTermConditions(): Collection
    {
        return $this->quantityInquiryTermConditions;
    }

    public function addQuantityInquiryTermCondition(QuantityInquiryTermCondition $quantityInquiryTermCondition): static
    {
        if (!$this->quantityInquiryTermConditions->contains($quantityInquiryTermCondition)) {
            $this->quantityInquiryTermConditions->add($quantityInquiryTermCondition);
            $quantityInquiryTermCondition->setQuantityInquiry(quantityInquiry: $this);
        }

        return $this;
    }

    public function getCustomerEmailSentAt(): ?\DateTimeImmutable
    {
        return $this->customerEmailSentAt;
    }

    public function setCustomerEmailSentAt(?\DateTimeImmutable $customerEmailSentAt): static
    {
        $this->customerEmailSentAt = $customerEmailSentAt;

        return $this;
    }

    public function getSalesEmailSentAt(): ?\DateTimeImmutable
    {
        return $this->salesEmailSentAt;
    }

    public function setSalesEmailSentAt(?\DateTimeImmutable $salesEmailSentAt): static
    {
        $this->salesEmailSentAt = $salesEmailSentAt;

        return $this;
    }
}
