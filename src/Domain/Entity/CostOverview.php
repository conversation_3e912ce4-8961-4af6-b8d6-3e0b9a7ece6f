<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\CostOverviewRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CostOverviewRepository::class)]
class CostOverview implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(length: 250)]
    private string $text;

    #[ORM\Column(nullable: true)]
    private ?bool $textHighlight = false;

    #[ORM\Column(type: Types::INTEGER)]
    private int $sequence;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $area = null;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $currency = null;

    #[ORM\Column(nullable: true)]
    private ?float $price = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $priceText = null;

    #[ORM\Column(nullable: true)]
    private ?bool $priceTextHighlight = false;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $category = null;

    #[ORM\ManyToOne(inversedBy: 'costOverviews')]
    #[ORM\JoinColumn(nullable: false)]
    private Model $model;

    public function __construct()
    {
        $this->init();
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(string $text): static
    {
        $this->text = $text;

        return $this;
    }

    public function getSequence(): int
    {
        return $this->sequence;
    }

    public function setSequence(int $sequence): static
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    public function setModel(Model $model): static
    {
        $this->model = $model;

        return $this;
    }

    public function getArea(): ?string
    {
        return $this->area;
    }

    public function setArea(?string $area): static
    {
        $this->area = $area;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(?float $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getPriceText(): ?string
    {
        return $this->priceText;
    }

    public function setPriceText(?string $priceText): static
    {
        $this->priceText = $priceText;

        return $this;
    }

    public function getTextHighlight(): ?bool
    {
        return $this->textHighlight;
    }

    public function setTextHighlight(?bool $textHighlight): static
    {
        $this->textHighlight = $textHighlight;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getPriceTextHighlight(): ?bool
    {
        return $this->priceTextHighlight;
    }

    public function setPriceTextHighlight(?bool $priceTextHighlight): static
    {
        $this->priceTextHighlight = $priceTextHighlight;

        return $this;
    }
}
