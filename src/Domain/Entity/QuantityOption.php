<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantityOptionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantityOptionRepository::class)]
class QuantityOption implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $parentId = null;

    #[ORM\Column(length: 25)]
    private string $name;

    #[ORM\Column(length: 25)]
    private string $inputType;

    #[ORM\Column(length: 25, nullable: false)]
    private string $productType;

    /**
     * @var Collection<int, QuantityOptionContent>
     */
    #[ORM\OneToMany(targetEntity: QuantityOptionContent::class, mappedBy: 'quantityOption')]
    private Collection $quantityOptionContents;

    /**
     * @var Collection<int, QuantitySelectionContent>
     */
    #[ORM\OneToMany(targetEntity: QuantitySelectionContent::class, mappedBy: 'quantityOption')]
    private Collection $quantitySelectionContents;

    public function __construct()
    {
        $this->init();
        $this->quantityOptionContents = new ArrayCollection();
        $this->quantitySelectionContents = new ArrayCollection();
    }

    public function getParentId(): ?string
    {
        return $this->parentId;
    }

    public function setParentId(?string $parentId): static
    {
        $this->parentId = $parentId;

        return $this;
    }

    public function getProductType(): string
    {
        return $this->productType;
    }

    public function setProductType(string $productType): static
    {
        $this->productType = $productType;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getInputType(): string
    {
        return $this->inputType;
    }

    public function setInputType(string $inputType): static
    {
        $this->inputType = $inputType;

        return $this;
    }

    /**
     * @return Collection<int, QuantityOptionContent>
     */
    public function getQuantityOptionContents(): Collection
    {
        return $this->quantityOptionContents;
    }

    /**
     * @return Collection<int, QuantitySelectionContent>
     */
    public function getQuantitySelectionContents(): Collection
    {
        return $this->quantitySelectionContents;
    }

    public function addQuantitySelectionContent(QuantitySelectionContent $quantitySelectionContent): static
    {
        if (!$this->quantitySelectionContents->contains($quantitySelectionContent)) {
            $this->quantitySelectionContents->add($quantitySelectionContent);
            $quantitySelectionContent->setQuantityOption(quantityOption: $this);
        }

        return $this;
    }
}
