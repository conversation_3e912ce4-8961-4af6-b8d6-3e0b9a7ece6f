<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\ModelRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ModelRepository::class)]
class Model implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(length: 25)]
    private string $type;

    #[ORM\Column(length: 25, nullable: true)]
    private ?string $classification = null;

    #[ORM\Column(length: 25)]
    private string $title;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $sequence = null;

    #[ORM\Column(length: 150)]
    private string $subtitle;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $currency = null;

    #[ORM\Column(nullable: true)]
    private ?float $price = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $priceText = null;

    #[ORM\Column(length: 150, nullable: true)]
    private ?string $priceSubtitle = null;

    #[ORM\Column(nullable: true)]
    private ?float $thresholdMin = null;

    #[ORM\Column(nullable: true)]
    private ?float $thresholdMax = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prezeroContact = null;

    /**
     * @var Collection<int, IncludedService>
     */
    #[ORM\OneToMany(targetEntity: IncludedService::class, mappedBy: 'model')]
    private Collection $includedServices;

    #[ORM\Column(length: 25, nullable: false)]
    private string $productType;

    /**
     * @var Collection<int, QuantitySelection>
     */
    #[ORM\OneToMany(targetEntity: QuantitySelection::class, mappedBy: 'model')]
    private Collection $quantitySelections;

    /**
     * @var Collection<int, TermCondition>
     */
    #[ORM\OneToMany(targetEntity: TermCondition::class, mappedBy: 'model')]
    private Collection $termConditions;

    /**
     * @var Collection<int, Agreement>
     */
    #[ORM\OneToMany(targetEntity: Agreement::class, mappedBy: 'model')]
    private Collection $agreements;

    public function __construct()
    {
        $this->init();
        $this->includedServices = new ArrayCollection();
        $this->quantitySelections = new ArrayCollection();
        $this->termConditions = new ArrayCollection();
        $this->agreements = new ArrayCollection();
    }

    /**
     * @return Collection<int, IncludedService>
     */
    public function getIncludedServices(): Collection
    {
        return $this->includedServices;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getClassification(): ?string
    {
        return $this->classification;
    }

    public function setClassification(?string $classification): static
    {
        $this->classification = $classification;

        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getSequence(): ?int
    {
        return $this->sequence;
    }

    public function setSequence(?int $sequence): static
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getSubtitle(): string
    {
        return $this->subtitle;
    }

    public function setSubtitle(string $subtitle): static
    {
        $this->subtitle = $subtitle;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(?float $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): static
    {
        $this->currency = $currency;

        return $this;
    }

    public function getPrezeroContact(): ?string
    {
        return $this->prezeroContact;
    }

    public function setPrezeroContact(?string $prezeroContact): static
    {
        $this->prezeroContact = $prezeroContact;

        return $this;
    }

    public function getPriceText(): ?string
    {
        return $this->priceText;
    }

    public function setPriceText(?string $priceText): static
    {
        $this->priceText = $priceText;

        return $this;
    }

    public function getPriceSubtitle(): ?string
    {
        return $this->priceSubtitle;
    }

    public function setPriceSubtitle(?string $priceSubtitle): static
    {
        $this->priceSubtitle = $priceSubtitle;

        return $this;
    }

    public function getThresholdMin(): ?float
    {
        return $this->thresholdMin;
    }

    public function setThresholdMin(?float $thresholdMin): static
    {
        $this->thresholdMin = $thresholdMin;

        return $this;
    }

    public function getThresholdMax(): ?float
    {
        return $this->thresholdMax;
    }

    public function setThresholdMax(?float $thresholdMax): static
    {
        $this->thresholdMax = $thresholdMax;

        return $this;
    }

    public function getProductType(): string
    {
        return $this->productType;
    }

    public function setProductType(string $productType): static
    {
        $this->productType = $productType;

        return $this;
    }

    /**
     * @return Collection<int, QuantitySelection>
     */
    public function getQuantitySelections(): Collection
    {
        return $this->quantitySelections;
    }

    public function addQuantitySelection(QuantitySelection $quantitySelection): static
    {
        if (!$this->quantitySelections->contains($quantitySelection)) {
            $this->quantitySelections->add($quantitySelection);
            $quantitySelection->setModel(model: $this);
        }

        return $this;
    }

    public function removeQuantitySelection(QuantitySelection $quantitySelection): static
    {
        // set the owning side to null (unless already changed)
        if ($this->quantitySelections->removeElement($quantitySelection) && $quantitySelection->getModel() === $this) {
            // $quantitySelection->setModel(model: null);
        }

        return $this;
    }

    /**
     * @return Collection<int, TermCondition>
     */
    public function getTermConditions(): Collection
    {
        return $this->termConditions;
    }

    public function addTermCondition(TermCondition $termCondition): static
    {
        if (!$this->termConditions->contains($termCondition)) {
            $this->termConditions->add($termCondition);
            $termCondition->setModel(model: $this);
        }

        return $this;
    }

    /**
     * @return Collection<int, Agreement>
     */
    public function getAgreements(): Collection
    {
        return $this->agreements;
    }

    public function addAgreement(Agreement $agreement): static
    {
        if (!$this->agreements->contains($agreement)) {
            $this->agreements->add($agreement);
            $agreement->setModel(model: $this);
        }

        return $this;
    }
}
