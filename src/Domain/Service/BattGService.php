<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Domain\Entity\Enum\ProductType;
use App\Domain\Entity\Enum\QuantityInputType;
use App\Domain\Entity\QuantityOption;
use App\Domain\Entity\QuantityOptionContent;
use App\Domain\Repository\QuantityOptionContentRepository;
use App\Domain\Repository\QuantityOptionRepository;
use App\Infrastructure\Shop\Resource\QuantitySelection;

readonly class BattGService
{
    public function __construct(
        private QuantityOptionRepository $quantityOptionRepository,
        private QuantityOptionContentRepository $quantityOptionContentRepository,
    ) {
    }

    public function calculate(QuantitySelection $quantitySelection): float
    {
        $totalAmount = 0;
        foreach ($quantitySelection->quantities as $quantity) {
            $quantityOptionContent = null;
            $quantityOptionAmount = null;

            foreach ($quantity->selections as $selection) {
                $quantityOption = $this->quantityOptionRepository->findOneBy(criteria: ['productType' => ProductType::BATTG->value, 'name' => $selection->quantityOptionId]);

                if (!$quantityOption instanceof QuantityOption) {
                    throw new \Exception(message: 'Quantity option not found: '.$selection->quantityOptionId);
                }

                if ($quantityOption->getInputType() === QuantityInputType::DROPDOWN->value) {
                    if (false === uuid_is_valid(uuid: $selection->quantityOptionValue)) {
                        throw new \Exception(message: 'Invalid quantity option id: '.$selection->quantityOptionValue);
                    }

                    $quantityOptionContent = $this->quantityOptionContentRepository->find(id: $selection->quantityOptionValue);
                    if (!$quantityOptionContent instanceof QuantityOptionContent) {
                        throw new \Exception(message: 'Quantity option content not found: '.$selection->quantityOptionValue);
                    }
                } elseif ($quantityOption->getInputType() === QuantityInputType::NUMBER->value) {
                    $quantityOptionAmount = $selection->quantityOptionValue;
                }
            }

            if (is_numeric(value: $quantityOptionAmount) && !is_null(value: $quantityOptionContent)) {
                $amount = $quantityOptionAmount * $quantityOptionContent->getPrice();
                $totalAmount += $amount;
            }
        }

        return $totalAmount;
    }
}
