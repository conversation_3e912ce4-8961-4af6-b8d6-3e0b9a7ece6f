<?php

declare(strict_types=1);

namespace App\Domain\Service\ObjectStorage;

readonly class ObjectMetadata
{
    /**
     * @param array<string, string>|null $misc
     */
    public function __construct(
        public ?string $tenantIdentifier = null,
        public ?string $userIdentifier = null,
        public ?array $misc = null,
    ) {
    }

    /**
     * @return array<string, string>
     */
    public function toArray(): array
    {
        $metadataArray = [];

        if (null !== $this->userIdentifier) {
            $metadataArray['user_identifier'] = $this->userIdentifier;
        }

        if (null !== $this->tenantIdentifier) {
            $metadataArray['tenant_identifier'] = $this->tenantIdentifier;
        }

        if (null !== $this->misc) {
            $metadataArray = array_merge($metadataArray, $this->misc);
        }

        return $metadataArray;
    }
}
