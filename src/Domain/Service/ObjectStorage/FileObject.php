<?php

declare(strict_types=1);

namespace App\Domain\Service\ObjectStorage;

use Aws\Api\DateTimeResult;

readonly class FileObject
{
    public function __construct(
        public string $identifier,
        public string $content,
        public DateTimeResult $lastModified = new DateTimeResult(),
        public string $mimeType = 'application/octet-stream',
        public ?ObjectMetadata $objectMetadata = null,
    ) {
    }
}
