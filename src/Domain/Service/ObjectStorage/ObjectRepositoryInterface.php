<?php

declare(strict_types=1);

namespace App\Domain\Service\ObjectStorage;

interface ObjectRepositoryInterface
{
    /**
     * @throws ObjectStorageException
     */
    public function store(FileObject $object): void;

    /**
     * @throws ObjectStorageException
     */
    public function get(string $identifier): ?FileObject;

    /**
     * @throws ObjectStorageException
     */
    public function exists(string $identifier): bool;

    /**
     * @throws ObjectStorageException
     */
    public function metadata(string $identifier): ?ObjectMetadata;

    /**
     * @throws ObjectStorageException
     */
    public function delete(string $identifier): void;
}
