<?php

declare(strict_types=1);

namespace App\Domain\ValueObject;

readonly class FileDefinition implements \Stringable
{
    public function __construct(
        public string $file,
        public string $name,
        public ?string $label = null,
    ) {
    }

    public static function fromSerializedValue(string $serializedValue): self
    {
        try {
            $value = json_decode(json: $serializedValue, associative: true, depth: 512, flags: JSON_THROW_ON_ERROR);
        } catch (\JsonException $e) {
            throw new \RuntimeException(message: $e->getMessage(), code: $e->getCode(), previous: $e);
        }

        if (
            !is_array(value: $value)
            || !array_key_exists(key: 'file', array: $value)
            || !is_string(value: $value['file'])
            || !array_key_exists(key: 'name', array: $value)
            || !is_string(value: $value['name'])
        ) {
            throw new \InvalidArgumentException(message: 'Invalid serialized value for FileDefinition: '.$serializedValue);
        }

        return new self(
            file: $value['file'],
            name: $value['name'],
            label: is_string(value: $value['label']) ? $value['label'] : null,
        );
    }

    public function __toString(): string
    {
        return $this->file;
    }
}
