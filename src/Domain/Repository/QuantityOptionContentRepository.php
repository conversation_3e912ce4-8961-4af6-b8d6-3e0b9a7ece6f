<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\QuantityOption;
use App\Domain\Entity\QuantityOptionContent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<QuantityOptionContent>
 */
class QuantityOptionContentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, QuantityOptionContent::class);
    }

    /**
     * @return array<QuantityOptionContent>
     *
     * @throws \Exception
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $productType, string $quantityOption): array
    {
        $parentQuantityContentOptionId = $userSearchCriteria->filters['selectedParentQuantityContentOptionId']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'quantityOptionContent')
            ->innerJoin(join: QuantityOption::class, alias: 'quantityOption', conditionType: 'WITH', condition: 'quantityOptionContent.quantityOption = quantityOption.id')
            ->andWhere('quantityOption.name = :quantityOption')
            ->andWhere('quantityOption.productType = :productType')
            ->setParameter(key: 'quantityOption', value: $quantityOption)
            ->setParameter(key: 'productType', value: $productType);

        if ($parentQuantityContentOptionId) {
            $qb->andWhere('quantityOptionContent.parentId = :parentQuantityContentOptionId')
                ->setParameter(key: 'parentQuantityContentOptionId', value: $parentQuantityContentOptionId);
        } else {
            $qb->andWhere('quantityOptionContent.parentId IS NULL');
        }

        /** @var array<QuantityOptionContent> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
