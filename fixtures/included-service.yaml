App\Domain\Entity\IncludedService:
    battg_model_1_included_service_1:
        text: '<PERSON><PERSON><PERSON><PERSON><PERSON> inkl. Verwaltungspauschale und Mindestvergütung'
        sequence: 1
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_1_included_service_2:
        text: 'Jährliche Mengenmeldung'
        sequence: 2
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_1_included_service_3:
        text: 'Einmalige Zahlung pro Jahr, auf Rechnung '
        sequence: 3
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_1_included_service_4:
        text: 'Online Vertragsabschluss ohne Wartezeit'
        sequence: 4
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_1_included_service_cost_1:
        text: 'Entsorgungskostenbeiträge für bis zu 300¹ kg Batterien pro Jahr'
        sequence: 1
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_1_included_service_cost_2:
        text: 'Einmalige Mengenmeldung am Jahresende im Online-Portal'
        sequence: 2
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_1_included_service_cost_3:
        text: 'Online Vertragsabschluss'
        sequence: 3
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_1_included_service_cost_not_1:
        text: 'EAR-Registrierung'
        sequence: 1
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    battg_model_1_included_service_cost_not_2:
        text: 'Stellung eines Bevollmächtigten'
        sequence: 2
        model: '@battg_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false

    battg_model_2_included_service_1:
        text: 'Zahlung von Verwaltungspauschale bei Vertragsschluss'
        sequence: 1
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_2_included_service_2:
        text: 'Monatliche Mengenmeldung'
        sequence: 2
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_2_included_service_3:
        text: 'Zahlung der Entsorgungskostenbeiträge nach Mengenmeldung'
        sequence: 3
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_2_included_service_4:
        text: 'Online Vertragsabschluss ohne Wartezeit'
        sequence: 4
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_2_included_service_cost_1:
        text: 'Erfüllung der Sammelquote mit über 4.000 Rücknahmestellen in Deutschland'
        sequence: 1
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_2_included_service_cost_2:
        text: 'Monatliche Mengenmeldung im Online-Portal'
        sequence: 2
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_2_included_service_cost_3:
        text: 'Online Vertragsabschluss'
        sequence: 3
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    battg_model_2_included_service_cost_not_1:
        text: 'EAR-Registrierung'
        sequence: 1
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    battg_model_2_included_service_cost_not_2:
        text: 'Stellung eines Bevollmächtigten'
        sequence: 2
        model: '@battg_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false

    battg_model_3_included_service_1:
        text: 'Verwaltungspauschale entfällt'
        sequence: 1
        model: '@battg_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_3_included_service_2:
        text: 'Individuelles Angebot in Abstimmung mit unseren Vertrieb'
        sequence: 2
        model: '@battg_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_3_included_service_3:
        text: 'Monatliche Mengenmeldung'
        sequence: 3
        model: '@battg_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    battg_model_3_included_service_4:
        text: 'Zahlung der Entsorgungskostenbeiträge nach Mengenmeldung'
        sequence: 4
        model: '@battg_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true

    weee_model_1_included_service_1:
        text: 'Jährlicher Festpreis'
        sequence: 1
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_1_included_service_2:
        text: 'Inklusive einer Abholung oder einer Behältererstgestellung pro Jahr*'
        sequence: 2
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_1_included_service_3:
        text: 'Einmalige Zahlung, auf Rechnung'
        sequence: 3
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_1_included_service_cost_1:
        text: 'Festpreis inkl. einer Abholung oder Behältererstgestellung pro Jahr'
        sequence: 1
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_1_included_service_cost_2:
        text: 'Einmalige Zahlung, auf Rechnung'
        sequence: 2
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_1_included_service_cost_3:
        text: 'Jährliche Abrechnung, falls weitere Abholung/Behältererstgestellung erforderlich'
        sequence: 3
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_1_included_service_cost_not_1:
        text: 'EAR-Registrierung'
        sequence: 1
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_1_included_service_cost_not_2:
        text: 'Insolvenzsichere Garantie'
        sequence: 2
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_1_included_service_cost_not_3:
        text: 'Input- und Output-Mitteilungen (Mengenmeldungen)'
        sequence: 3
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_1_included_service_cost_not_4:
        text: 'Stellung eines Bevollmächtigten'
        sequence: 3
        model: '@weee_model_1'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false

    weee_model_2_included_service_1:
        text: 'Preis pro Abholvorgang basierend auf Geräteart und -menge'
        sequence: 1
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_2_included_service_2:
        text: 'Preis pro Erstgestellungsvorgang basierend auf Behälterart und -anzahl'
        sequence: 2
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_2_included_service_3:
        text: 'Zahlung monatlich, nach jeweils erfolgter Leistung, auf Rechnung'
        sequence: 3
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_2_included_service_4:
        text: 'jährliche Mindestvergütung i.H.v. € 295,00, falls in einem Vertragsjahr kein Abhol-/Erstgestellungsauftrag beauftragt wird '
        sequence: 4
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_2_included_service_5:
        text: 'Zahlung zum Ende des jeweiligen Kalenderjahres, auf Rechnung'
        sequence: 5
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_2_included_service_cost_1:
        text: 'Fester Preis pro Abhol- bzw. Behältergestellungsvorgang'
        sequence: 1
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_2_included_service_cost_2:
        text: 'Zahlung monatlich, nach jeweils erfolgter Leistung, auf Rechnung'
        sequence: 2
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_2_included_service_cost_3:
        text: 'jährliche Mindestvergütung i.H.v. € 295,00, falls in einem Vertragsjahr kein Abhol-/Erstgestellungsauftrag beauftragt wird, zahlbar zum Ende des jeweiligen Kalenderjahres, auf Rechnung'
        sequence: 3
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: true
    weee_model_2_included_service_cost_not_1:
        text: 'EAR-Registrierung'
        sequence: 1
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_2_included_service_cost_not_2:
        text: 'Insolvenzsichere Garantie'
        sequence: 2
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_2_included_service_cost_not_3:
        text: 'Input- und Output-Mitteilungen (Mengenmeldungen)'
        sequence: 3
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false
    weee_model_2_included_service_cost_not_4:
        text: 'Stellung eines Bevollmächtigten'
        sequence: 3
        model: '@weee_model_2'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::COST->value)>'
        included: false

    weee_model_3_included_service_1:
        text: 'Preis pro Abholvorgang basierend auf Geräteart und -menge'
        sequence: 1
        model: '@weee_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_3_included_service_2:
        text: 'Preis pro Erstgestellungsvorgang basierend auf Behälterart und -anzahl'
        sequence: 2
        model: '@weee_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_3_included_service_3:
        text: 'Zahlung monatlich, nach jeweils erfolgter Leistung, auf Rechnung'
        sequence: 3
        model: '@weee_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_3_included_service_4:
        text: 'jährliche Mindestvergütung i.H.v. € 295,00, falls in einem Vertragsjahr kein Abhol-/Erstgestellungsauftrag beauftragt wird'
        sequence: 4
        model: '@weee_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
    weee_model_3_included_service_5:
        text: 'Zahlung zum Ende des jeweiligen Kalenderjahres, auf Rechnung'
        sequence: 5
        model: '@weee_model_3'
        page: '<(App\Domain\Entity\Enum\IncludedServicePage::OVERVIEW->value)>'
        included: true
