App\Domain\Entity\QuantityOption:
    quantity_option_1:
        name: 'battg-type'
        inputType: '<(App\Domain\Entity\Enum\QuantityInputType::DROPDOWN->value)>'
        parentId: null
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
    quantity_option_2:
        name: 'battg-unit-weight'
        inputType: '<(App\Domain\Entity\Enum\QuantityInputType::DROPDOWN->value)>'
        parentId: '@quantity_option_1->id'
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
    quantity_option_3:
        name: 'battg-amount'
        inputType: '<(App\Domain\Entity\Enum\QuantityInputType::NUMBER->value)>'
        parentId: null
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
    quantity_option_4:
        name: 'weee-type'
        inputType: '<(App\Domain\Entity\Enum\QuantityInputType::DROPDOWN->value)>'
        parentId: null
        productType: '<(App\Domain\Entity\Enum\ProductType::WEEE->value)>'
    quantity_option_5:
        name: 'weee-amount'
        inputType: '<(App\Domain\Entity\Enum\QuantityInputType::NUMBER->value)>'
        parentId: null
        productType: '<(App\Domain\Entity\Enum\ProductType::WEEE->value)>'
