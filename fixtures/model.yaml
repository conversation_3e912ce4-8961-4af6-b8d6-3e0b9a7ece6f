App\Domain\Entity\Model:
    battg_model_1:
        type: '<(App\Domain\Entity\Enum\ModelType::ORDERING->value)>'
        title: 'Basic'
        subtitle: 'Inklusive 300 kg¹ pro Jahr '
        threshold_min: 0
        threshold_max: 300
        price: 450
        currency: '€'
        priceText: '{{currency}} {{price}},-'
        priceSubtitle: '¹ Die Menge ist abhängig von der Batterieart '
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
        prezeroContact: 'Anne-<PERSON><EMAIL>;<PERSON>.<PERSON><EMAIL>;<EMAIL>;<EMAIL>;<PERSON>@prezero.com'
        classification: '<(App\Domain\Entity\Enum\Classification::BASIC->value)>'
        sequence: 1
    battg_model_2:
        type: '<(App\Domain\Entity\Enum\ModelType::ORDERING->value)>'
        title: 'Advanced'
        subtitle: 'Ab 300 kg pro Jahr'
        threshold_min: 300
        threshold_max: 10000
        price: 150
        currency: '€'
        priceText: '{{currency}} {{price}},-'
        priceSubtitle: 'zzgl. monatlicher Zahlungen'
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
        prezeroContact: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
        classification: '<(App\Domain\Entity\Enum\Classification::ADVANCED->value)>'
        sequence: 2
    battg_model_3:
        type: '<(App\Domain\Entity\Enum\ModelType::INQUIRY->value)>'
        title: 'Premium'
        subtitle: 'Ab ca. 10.000 kg pro Jahr'
        threshold_min: 10000
        threshold_max: 999999999
        price: null
        currency: null
        priceText: 'Individuelle Konditionen'
        priceSubtitle: null
        productType: '<(App\Domain\Entity\Enum\ProductType::BATTG->value)>'
        prezeroContact: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
        classification: '<(App\Domain\Entity\Enum\Classification::PREMIUM->value)>'
        sequence: 3

    weee_model_1:
        type: '<(App\Domain\Entity\Enum\ModelType::ORDERING->value)>'
        title: 'Basic'
        subtitle: 'Kleingeräte bis max. 50 t Inputmenge'
        threshold_min: -999999999
        threshold_max: 0
        price: 450
        currency: '€'
        priceText: '{{currency}} {{price}},- pro Jahr'
        priceSubtitle: '* Etwaige darüber hinausgehende Abhol-/Erstgestellungsvorgänge werden im Bedarfsfall zu gesonderten Konditionen abgerechnet.'
        productType: '<(App\Domain\Entity\Enum\ProductType::WEEE->value)>'
        prezeroContact: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
        classification: '<(App\Domain\Entity\Enum\Classification::BASIC->value)>'
        sequence: 1

    weee_model_2:
        type: '<(App\Domain\Entity\Enum\ModelType::ORDERING->value)>'
        title: 'Advanced'
        subtitle: 'Alle Gerätearten, die in privaten Haushalten genutzt werden können'
        threshold_min: 0
        threshold_max: 5000
        price: null
        currency: null
        priceText: 'Jährliche Entsorgungskosten'
        priceSubtitle: 'oder 295,00 € Mindestvergütung'
        productType: '<(App\Domain\Entity\Enum\ProductType::WEEE->value)>'
        prezeroContact: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
        classification: '<(App\Domain\Entity\Enum\Classification::ADVANCED->value)>'
        sequence: 2

    weee_model_3:
        type: '<(App\Domain\Entity\Enum\ModelType::INQUIRY->value)>'
        title: 'Premium'
        subtitle: 'Individuelles Angebot ab € 5.000,- jährlichen Entsorgungskosten; alle Gerätearten, die in privaten Haushalten genutzt werden können'
        threshold_min: 5000
        threshold_max: 999999999
        price: null
        currency: null
        priceText: 'Jährliche Entsorgungskosten'
        priceSubtitle: 'oder 295,00 € Mindestvergütung'
        productType: '<(App\Domain\Entity\Enum\ProductType::WEEE->value)>'
        prezeroContact: '<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>'
        classification: '<(App\Domain\Entity\Enum\Classification::PREMIUM->value)>'
        sequence: 3
