server:
    disable: true
clients:
    - url: https://logs.stackit3.argus.eu01.stackit.cloud/instances/${ARGUS_INSTANCE_ID}/loki/api/v1/push
      headers:
          Authorization: Basic ${ARGUS_BASIC_AUTH}
scrape_configs:
    - job_name: logs
      pipeline_stages:
          - json:
                expressions:
                    channel:
                    level:
                    level_name:
          - labels:
                channel:
                level:
                level_name:
          - labeldrop:
                - filename
      static_configs:
          - targets:
                - localhost
            labels:
                __path__: /app/var/log/*.log
                __path_exclude__: /app/var/log/deprecated.log
                app: ${APP_NAME}
