#!/bin/bash

set -e

echo "Configuring Logs Alert Groups"

# Get all available log alert groups
LOGS_ALERT_GROUPS=$(
    curl -X GET \
        -s "https://api.argus.eu01.stackit.cloud/v1/instances/${ARGUS_INSTANCE_ID}/logs-alertgroups" \
        -H "Authorization: Basic ${ARGUS_BASIC_AUTH}" \
        | jq -r '.data[].name'
)

# Delete all groups
for group in $LOGS_ALERT_GROUPS
do
    curl -X DELETE \
        -s "https://api.argus.eu01.stackit.cloud/v1/instances/${ARGUS_INSTANCE_ID}/logs-alertgroups/$group" \
        -H "Authorization: Basic ${ARGUS_BASIC_AUTH}"
done

LOGS_ALERT_GROUPS_DIR="monitoring/argus/logs-alert-groups/*.json"

# Create alert groups
for payload in $LOGS_ALERT_GROUPS_DIR
do
    curl -X POST \
        --data "@$payload" \
        -s "https://api.argus.eu01.stackit.cloud/v1/instances/${ARGUS_INSTANCE_ID}/logs-alertgroups" \
        -H "Authorization: Basic ${ARGUS_BASIC_AUTH}" \
        -H "Content-Type: application/json"
done
