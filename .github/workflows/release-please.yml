name: Releases

on:
    push:
        branches:
            - master
            - release
    workflow_dispatch:

jobs:
    release-please:
        runs-on: prezero-github-runner
        outputs:
            release_created: ${{ steps.release.outputs.release_created }}
            tag_name: ${{ steps.release.outputs.tag_name }}
        steps:
            - uses: googleapis/release-please-action@c2a5a2bd6a758a0937f1ddb1e8950609867ed15c # v4
              id: release
              with:
                  token: ${{ secrets.PREZERO_GITHUB_TOKEN }}
                  target-branch: ${{ github.ref_name }}

    build-release-images:
        name: Build release images
        needs: [release-please]
        if: ${{ needs.release-please.outputs.release_created == 'true' }}
        uses: prezero/workflows/.github/workflows/docker-build.yaml@88084636117e9de59f5ca50596fd39a17c8cf2be # v1.35.0
        with:
            ENV: prod
            DOCKERFILE: docker/Dockerfile
            IMAGE: ghcr.io/${{ github.repository }}
            TAG: ${{ needs.release-please.outputs.tag_name }}
            RELEASE_CREATED: ${{ needs.release-please.outputs.release_created == 'true' }}
        secrets: inherit
