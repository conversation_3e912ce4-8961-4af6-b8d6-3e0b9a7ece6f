name: DEV Deployment

on:
    push:
        branches:
            - master
    workflow_dispatch:

concurrency:
    group: license-shop-sf-deployment-dev
    cancel-in-progress: false

env:
    IMAGE_NAME: ghcr.io/${{ github.repository }}
    IMAGE_TAG: dev

jobs:
    setup-env:
        runs-on: prezero-github-runner
        environment: dev
        outputs:
            IMAGE_NAME: ${{ steps.setup-env.outputs.IMAGE_NAME }}
            IMAGE_TAG: ${{ steps.setup-env.outputs.IMAGE_TAG }}
            HEALTHCHECK_URL: ${{ steps.setup-env.outputs.HEALTHCHECK_URL }}
            ARGOCD_URL: ${{ steps.setup-env.outputs.ARGOCD_URL }}
        steps:
            - name: Setup Environment
              id: setup-env
              run: |
                  echo "IMAGE_NAME=${{env.IMAGE_NAME}}" >> $GITHUB_OUTPUT;
                  echo "IMAGE_TAG=${{env.IMAGE_TAG}}" >> $GITHUB_OUTPUT;
                  echo "HEALTHCHECK_URL=${{ vars.HEALTHCHECK_URL }}" >> $GITHUB_OUTPUT;
                  echo "ARGOCD_URL=${{ vars.ARGOCD_URL }}" >> $GITHUB_OUTPUT;
    build-and-deploy:
        needs: [setup-env]
        uses: prezero/workflows/.github/workflows/deployment.yaml@88084636117e9de59f5ca50596fd39a17c8cf2be # v1.35.0
        secrets: inherit
        with:
            ENV: dev
            DOCKERFILE: docker/Dockerfile
            IMAGE: ${{ needs.setup-env.outputs.IMAGE_NAME }}
            TAG: ${{ needs.setup-env.outputs.IMAGE_TAG }}
            RELEASE_CREATED: false
            TARGET_LAYER: "dev"
            deployment_repository: "prezero/myprezero-deployment"
            deployment_repository_branch: "main"
            component: "backend"
            kubernetes_type_name: "backend"
            project_short_name: "license-shop"
            ARGOCD_URL: ${{ needs.setup-env.outputs.ARGOCD_URL }}
            ARGOCD_APP_NAME: "license-shop-dev"
            HEALTHCHECK_URL: ${{ needs.setup-env.outputs.HEALTHCHECK_URL }}
            execute_php_tests: true
            execute_migrations: true
