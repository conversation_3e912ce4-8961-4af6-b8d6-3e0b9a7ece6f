name: Maintenance

on:
    workflow_dispatch:
        inputs:
            env:
                description: "Github environment and in which environment the maintenance should be enabled"
                required: true
                type: choice
                options:
                    - "dev"
                    - "qa"
                    - "prod"
            maintenance_route:
                description: "Apply/Remove maintenance redirect ?"
                required: true
                type: choice
                options:
                    - "remove"
                    - "apply"
            prod_maintenance:
                description: "(only for PROD deploy): Sure to apply maintenance on Production ?"
                required: false
                type: choice
                options:
                    - "no"
                    - "yes"
            component:
                description: "The component for which the maintenance should be applied"
                required: true
                type: string
                default: "backend"
            project_short_name:
                description: "The short name of the project"
                required: true
                type: string
                default: "license-shop"
            maintenance_redirect_url:
                description: "The url where the user should be redirected in case of a maintenance"
                required: true
                type: string
                default: "maintenance.prezero-central.runs.onstackit.cloud"

jobs:
    maintenance-deployment:
        name: "Process Maintenance"
        runs-on: prezero-github-runner
        environment: ${{ inputs.env }}
        if: inputs.env != 'prod' || inputs.prod_maintenance == 'yes'
        steps:
            - name: Process Maintenance
              uses: prezero/workflows/.github/actions/maintenance@88084636117e9de59f5ca50596fd39a17c8cf2be # v1.35.0
              with:
                  ARGOCD_APP_NAME: "${{ inputs.project_short_name }}-${{ inputs.env }}"
                  ARGOCD_AUTH_TOKEN: ${{ secrets.ARGOCD_AUTH_TOKEN }}
                  ARGOCD_URL: ${{ vars.ARGOCD_URL }}
                  HEALTHCHECK_URL: ${{ vars.BACKEND_HEALTHCHECK_URL }}
                  HEALTHCHECK_BASIC_AUTH: ${{ secrets.BACKEND_BASIC_AUTH }}
                  PREZERO_GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
                  component: ${{ inputs.component }}
                  deployment_repository: "prezero/license-shop-deployment"
                  deployment_repository_branch: "main"
                  env: ${{ inputs.env }}
                  maintenance_route: ${{ inputs.maintenance_route }}
                  maintenance_redirect_url: ${{ inputs.maintenance_redirect_url }}
                  project_short_name: ${{ inputs.project_short_name }}
