# App
APP_ENV=localdev
APP_SECRET=fa6bc6ad4bbbd42ab6b086c04aeb14d4
DATA_ENV=dev
STDOUT_LOG_LEVEL=debug

# Database
DATABASE_URL='pgsql://user:userpwd@db:5432/license-shop'

# S3 compatible storage
S3_ACCESS_KEY=minio
S3_SECRET_KEY=minio123
S3_ENDPOINT_URL=http://s3storage:8003
S3_BUCKET_NAME=test-bucket

# Cors
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'

# Mailer
MAILER_DSN=smtp://mailcatcher:1025
MAIL_ENABLED=1
MAIL_BASE_LINK=http://localhost:8000
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=license-shop
EMAIL_REPLY_TO=<EMAIL>

# Misc
MOCK_SERVER_ENDPOINT=http://mockserver:1080
FILE_ENDPOINT=http://localhost:8000/api/shop/v1/file
