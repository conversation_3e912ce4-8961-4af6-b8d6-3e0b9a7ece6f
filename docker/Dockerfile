# --- START BASE ---------------------------------------------------------------- #
# hadolint ignore=DL3006
FROM ghcr.io/prezero/docker-images/franken:1.1.3-dev@sha256:64d5edc0d0ff85067d4722b6aa9d6be9b0216792dc00f5e387ffe642987e0bf7 AS base
LABEL org.opencontainers.image.source="https://github.com/prezero/license-shop-sf"
LABEL org.opencontainers.image.description="PreZero license-shop Image"

ENV SERVER_NAME=:8080

# --- END BASE ----------------------------------------------------------------- #

# --- START BUILD -------------------------------------------------------------- #
FROM base AS build

WORKDIR /app

ARG COMPOSER_AUTH
ENV COMPOSER_AUTH ${COMPOSER_AUTH}
ENV COMPOSER_ALLOW_SUPERUSER=1

COPY composer.* symfony.* package.* ./

RUN set -eux; \
    composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress;

# copy sources
COPY --link . ./

# --- END BUILD ---------------------------------------------------------------- #

# --- START DEV IMAGE --------------------------------------------------------- #
# hadolint ignore=DL3006
FROM ghcr.io/prezero/docker-images/franken:1.1.3@sha256:70bad4cfdbf8c6d594eec80d0ce73d43a523cce5b7ce7240a4d05bcb2183c58c AS dev
LABEL org.opencontainers.image.source="https://github.com/prezero/license-shop-sf"
LABEL org.opencontainers.image.description="PreZero license-shop Image"

WORKDIR /app

# Disable worker mode
ENV FRANKENPHP_CONFIG=""
# Disable auto https
ENV SERVER_NAME=:8080
# CF port
ENV PORT=8080

# Copy dependencies
COPY --chown=www-data:www-data --from=build /app .

RUN set -eux; \
    apk upgrade --no-cache; \
    mkdir -p var/cache var/log; \
    composer install --no-cache --prefer-dist --no-autoloader --no-scripts --no-progress; \
    composer dump-autoload --classmap-authoritative; \
    composer dump-env dev; \
    composer run-script post-install-cmd; \
    chmod +x bin/console bin/api-tests.sh; \
    php bin/console asset-map:compile; \
    chown -R www-data:www-data /app; \
    sync;
# --- END DEV IMAGE ----------------------------------------------------------- #


# --- START PROD IMAGE --------------------------------------------------------- #
# hadolint ignore=DL3006
FROM ghcr.io/prezero/docker-images/franken:1.1.3@sha256:70bad4cfdbf8c6d594eec80d0ce73d43a523cce5b7ce7240a4d05bcb2183c58c AS prod
LABEL org.opencontainers.image.source="https://github.com/prezero/license-shop-sf"
LABEL org.opencontainers.image.description="PreZero license-shop Image"

WORKDIR /app

# Disable worker mode
ENV FRANKENPHP_CONFIG=""
# Disable auto https
ENV SERVER_NAME=:8080
# CF port
ENV PORT=8080

# Copy dependencies
COPY --chown=www-data:www-data --from=build /app .

RUN set -eux; \
    apk upgrade --no-cache; \
    mkdir -p var/cache var/log; \
    composer dump-autoload --classmap-authoritative --no-dev; \
    composer dump-env prod; \
    composer run-script --no-dev post-install-cmd; \
    chmod +x bin/console bin/api-tests.sh; \
    php bin/console asset-map:compile; \
    chown -R www-data:www-data /app; \
    sync;
# --- END PROD IMAGE ----------------------------------------------------------- #
