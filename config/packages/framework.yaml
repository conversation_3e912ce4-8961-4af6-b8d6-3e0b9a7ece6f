# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    http_method_override: false
    handle_all_throwables: true
    set_locale_from_accept_language: true
    set_content_language_from_locale: true

    # Note that the session will be started ONLY if you read or write from it.
    session:
        enabled: false

    #esi: true
    #fragments: true

    php_errors:
        log: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
