# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    app.storage.bucket_name: '%env(S3_BUCKET_NAME)%'
    app.entity.path: '%kernel.project_dir%/src/Domain/Entity'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    s3_credentials:
        class: Aws\Credentials\Credentials
        arguments:
            - '%env(S3_ACCESS_KEY)%'
            - '%env(S3_SECRET_KEY)%'

    s3_client:
        class: Aws\S3\S3Client
        arguments:
            -   version: 'latest'
                region: 'us-east-1'
                credentials: '@s3_credentials'
                bucket_endpoint: false
                use_path_style_endpoint: true
                endpoint: '%env(S3_ENDPOINT_URL)%'

    App\Infrastructure\ObjectStorage\S3HealthCheck:
        autoconfigure: false
        tags:
            - { name: 'liip_monitor.check', alias: 's3' }

    PreZero\ApiBundle\Serializer\SerializerInterface:
        class: App\Infrastructure\Framework\ApiBundle\ApiBundleSerializer
