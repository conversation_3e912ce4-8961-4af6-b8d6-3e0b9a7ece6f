{"name": "prezero/license-shop-sf", "description": "Example Backend", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "^8.4", "ext-bcmath": "*", "ext-calendar": "*", "ext-ctype": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "ext-sockets": "*", "aws/aws-sdk-php": "^3.351.2", "doctrine/dbal": "^4.3", "doctrine/doctrine-bundle": "^2.15", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.5", "hautelook/alice-bundle": "^2.15", "liip/monitor-bundle": "^2.24", "nelmio/cors-bundle": "^2.5", "prezero/api-bundle": "^3.0.0", "symfony/asset": "7.3.*", "symfony/asset-mapper": "7.3.*", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2.8.1", "symfony/framework-bundle": "7.3.*", "symfony/mailer": "7.3.*", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "7.3.*", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/uid": "7.3.*", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^3.21.1", "vuryss/serializer": "^2.0.0"}, "require-dev": {"captainhook/captainhook": "^5.25.6", "codeception/codeception": "^5.3.2", "codeception/module-asserts": "^3.2", "codeception/module-phpbrowser": "^3.0.1", "codeception/module-rest": "^3.4", "codeception/module-symfony": "^3.6", "friendsofphp/php-cs-fixer": "^3.84", "phpstan/phpstan": "^2.1.18", "ramsey/conventional-commits": "^1.6.0", "rector/rector": "^2.1.2", "roave/security-advisories": "dev-latest", "savinmikhail/add_named_arguments_rector": "^0.1.17", "symfony/maker-bundle": "^1.64", "symfony/web-profiler-bundle": "7.3.*"}, "config": {"allow-plugins": {"captainhook/hook-installer": true, "codeception/c3": true, "php-http/discovery": true, "phpstan/extension-installer": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php83": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}, "captainhook": {"force-install": true, "only-enabled": false}, "ramsey/conventional-commits": {"config": {"types": ["perf", "revert", "chore", "docs", "style", "refactor", "test", "build", "ci"]}}}, "repositories": [{"name": "prezero/api-bundle", "type": "vcs", "url": "https://github.com/prezero/api-bundle"}]}