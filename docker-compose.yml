name: license-shop

services:
    backend:
        container_name: license-shop_backend
        platform: linux/amd64
        build:
            context: .
            dockerfile: docker/Dockerfile
            target: base
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes:
            - ${APP_VOLUME:-.}:/app
            - var:/app/var
            - caddy_data:/data
            - caddy_config:/config
        depends_on: [ 'db' ]
        ports:
            - '8000:8080'
        restart: unless-stopped
        environment:
            MERCURE_EXTRA_DIRECTIVES: demo
            MERCURE_PUBLISHER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
            MERCURE_SUBSCRIBER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
            TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
            TRUSTED_HOSTS: ^${SERVER_NAME:-license-shop\.com|localhost}|app
            PHP_IDE_CONFIG: serverName=${XDEBUG_SERVER_NAME:-license-shop}
            XDEBUG_MODE: ${XDEBUG_MODE:-debug} #debug
        extra_hosts:
            - host.docker.internal:host-gateway
        tty: true
        networks: [ license-shop_network ]
        healthcheck:
            test: [ "CMD-SHELL", "exec curl --head -fsS http://localhost:8080" ]
            interval: 10s
            timeout: 10s
            retries: 20

    db:
        container_name: license-shop_db
        image: postgres:17.5-alpine3.20@sha256:ccd14e0306cef4c2943a4bda5a2c6a77201a426eb2576d888e5cdc28ccddca75
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        environment:
            POSTGRES_DB: license-shop
            POSTGRES_USER: user
            POSTGRES_PASSWORD: userpwd
            PGDATA: /var/lib/postgresql/data/pgdata
        volumes:
            - database-data:/var/lib/postgresql/data
        ports:
            - '8001:5432'
        networks: [ license-shop_network ]

    mailcatcher:
        container_name: license-shop_mailcatcher
        platform: linux/amd64
        image: yappabe/mailcatcher@sha256:cc7241dc09fe299f899473cafd96ea68bd8f829485213abcae5b6ced16cfec8a
        ports:
            - '8002:1080'
        networks: [ license-shop_network ]

    s3storage:
        container_name: myprezero_s3storage
        image: quay.io/minio/minio:RELEASE.2025-07-23T15-54-02Z@sha256:d249d1fb6966de4d8ad26c04754b545205ff15a62e4fd19ebd0f26fa5baacbc0
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        ports:
            - '8003:8003'
            - '8004:8004'
        environment:
            MINIO_ROOT_USER: minio
            MINIO_ROOT_PASSWORD: minio123
            MINIO_REGION_NAME: eu01
        command:
            - 'server'
            - '/data'
            - '--address'
            - ':8003'
            - '--console-address'
            - ':8004'
        volumes:
            - s3-data:/data
        networks: [ license-shop_network ]
        healthcheck:
            test: [ "CMD-SHELL", "exec curl --head -fsS http://localhost:8003/minio/health/live" ]
            interval: 10s
            timeout: 10s
            retries: 20

    mockserver:
        container_name: license-shop_mockserver
        platform: linux/amd64
        image: mockserver/mockserver:5.15.0@sha256:0f9ef78c94894ac3e70135d156193b25e23872575d58e2228344964273b4af6b
        ports:
            - '8005:1080'
            - '8006:1090'
        networks: [ license-shop_network ]

volumes:
    var:
    caddy_data:
    caddy_config:
    database-data:
    s3-data:

networks:
    license-shop_network:
        name: license-shop_network
