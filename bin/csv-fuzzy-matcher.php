<?php

declare(strict_types=1);

/**
 * CSV Fuzzy Matcher Script
 *
 * This script compares two CSV files using fuzzy matching on the third column.
 * If the match is more than 75%, it adds the old key to a new column in the new file.
 *
 * Usage: php bin/csv-fuzzy-matcher.php <old_file.csv> <new_file.csv> <output_file.csv>
 */

if ($argc !== 4) {
    echo "Usage: php bin/csv-fuzzy-matcher.php <old_file.csv> <new_file.csv> <output_file.csv>\n";
    echo "Example: php bin/csv-fuzzy-matcher.php old_data.csv new_data.csv output_with_matches.csv\n";
    exit(1);
}

$oldFile = $argv[1];
$newFile = $argv[2];
$outputFile = $argv[3];

// Check if input files exist
if (!file_exists($oldFile)) {
    echo "Error: Old file '$oldFile' does not exist.\n";
    exit(1);
}

if (!file_exists($newFile)) {
    echo "Error: New file '$newFile' does not exist.\n";
    exit(1);
}

/**
 * Calculate similarity percentage between two strings using Levenshtein distance
 */
function calculateSimilarity(string $str1, string $str2): float
{
    $str1 = strtolower(trim($str1));
    $str2 = strtolower(trim($str2));

    if ($str1 === $str2) {
        return 100.0;
    }

    if (empty($str1) || empty($str2)) {
        return 0.0;
    }

    $maxLength = max(strlen($str1), strlen($str2));
    $levenshteinDistance = levenshtein($str1, $str2);

    return (1 - ($levenshteinDistance / $maxLength)) * 100;
}

/**
 * Read CSV file and return array of rows
 */
function readCsvFile(string $filename): array
{
    $rows = [];
    if (($handle = fopen($filename, 'r')) !== false) {
        while (($data = fgetcsv($handle, 0, ',', '"', '\\')) !== false) {
            $rows[] = $data;
        }
        fclose($handle);
    }
    return $rows;
}

/**
 * Write array of rows to CSV file
 */
function writeCsvFile(string $filename, array $rows): void
{
    if (($handle = fopen($filename, 'w')) !== false) {
        foreach ($rows as $row) {
            fputcsv($handle, $row, ',', '"', '\\');
        }
        fclose($handle);
    }
}

/**
 * Find best match for a given string in the old data
 */
function findBestMatch(string $searchValue, array $oldData): ?array
{
    $bestMatch = null;
    $bestSimilarity = 0.0;

    foreach ($oldData as $oldRow) {
        if (!isset($oldRow[2])) {
            continue; // Skip rows without third column
        }

        $similarity = calculateSimilarity($searchValue, $oldRow[2]);
        echo "  Comparing with '" . $oldRow[2] . "' -> " . round($similarity, 1) . "% similarity\n";

        if ($similarity > $bestSimilarity && $similarity >= 75.0) {
            $bestSimilarity = $similarity;
            $bestMatch = [
                'row' => $oldRow,
                'similarity' => $similarity,
                'old_key' => $oldRow[0] ?? '' // Assuming first column is the key
            ];
        }
    }

    return $bestMatch;
}

echo "Starting CSV fuzzy matching...\n";
echo "Old file: $oldFile\n";
echo "New file: $newFile\n";
echo "Output file: $outputFile\n\n";

// Read both CSV files
$oldData = readCsvFile($oldFile);
$newData = readCsvFile($newFile);

if (empty($oldData)) {
    echo "Error: Old file is empty or could not be read.\n";
    exit(1);
}

if (empty($newData)) {
    echo "Error: New file is empty or could not be read.\n";
    exit(1);
}

echo "Loaded " . count($oldData) . " rows from old file\n";
echo "Loaded " . count($newData) . " rows from new file\n\n";

// Process the data
$outputData = [];
$matchCount = 0;

foreach ($newData as $index => $newRow) {
    // Add the original row data
    $outputRow = $newRow;

    // Check if this row has a third column to match against
    if (isset($newRow[2]) && !empty(trim($newRow[2]))) {
        echo "Searching for matches for: '" . $newRow[2] . "'\n";
        $match = findBestMatch($newRow[2], $oldData);

        if ($match !== null) {
            // Add the old key as a new column
            $outputRow[] = $match['old_key'];
            $matchCount++;

            echo sprintf(
                "Match found for row %d: '%s' -> '%s' (%.1f%% similarity, old key: %s)\n",
                $index + 1,
                substr($newRow[2], 0, 50),
                substr($match['row'][2], 0, 50),
                $match['similarity'],
                $match['old_key']
            );
        } else {
            // No match found, add empty column
            $outputRow[] = '';
            echo "No match found (similarity < 75%)\n";
        }
    } else {
        // No third column or empty, add empty match column
        $outputRow[] = '';
        echo "Row " . ($index + 1) . " has no third column or is empty\n";
    }

    $outputData[] = $outputRow;
}

// Write the output file
writeCsvFile($outputFile, $outputData);

echo "\nProcessing complete!\n";
echo "Total rows processed: " . count($newData) . "\n";
echo "Matches found (≥75% similarity): $matchCount\n";
echo "Output written to: $outputFile\n";
echo "New column 'old_key' added to the rightmost position.\n";
